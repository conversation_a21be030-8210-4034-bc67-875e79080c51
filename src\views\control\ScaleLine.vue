<script setup>
import ScaleLine from 'ol/control/ScaleLine.js'
import { defineProps, watch } from 'vue'

const props = defineProps({
  map: Object
})

let scaleControl = null

const createScaleLine = (map) => {
  if (!map) {
    console.warn('ScaleLine: map 不存在');
    return
  }
  if (scaleControl) {
    console.log('ScaleLine: 移除已存在的比例尺控件')
    map.removeControl(scaleControl)
    scaleControl = null
  }
  scaleControl = new ScaleLine({
    units: 'metric' 
  })
  map.addControl(scaleControl)
  console.log('ScaleLine: 已添加比例尺控件到地图', map, scaleControl)
  setTimeout(() => {
    const el = document.querySelector('.ol-scale-line')
    if (el) {
      console.log('ScaleLine: .ol-scale-line DOM 已找到', el)
    } else {
      console.error('ScaleLine: .ol-scale-line DOM 未找到')
    }
  }, 1000)
}

watch(() => props.map, (map) => {
  if (map) createScaleLine(map)
}, { immediate: true })
</script>

<template>
</template>

<style>
.ol-scale-line {
  position: absolute;
  right: 20px !important;
  bottom: 20px !important;
  left: auto !important;
  background: rgba(255,255,255,0.95) !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.10) !important;
  border-radius: 12px !important;
  padding: 4px 16px !important;
  z-index: 10020 !important;
  height: 32px;
  display: flex;
  align-items: flex-end;
  pointer-events: none;
  border: 1px solid #e5e6eb !important;
}

/* 主横线和两端竖线 */
.ol-scale-line-inner {
  position: relative;
  height: 18px;
  margin: 0 2px;
  background: none !important;
  border: none !important;
}
.ol-scale-line-inner::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 0;
  border-bottom: 5px solid #111;
  border-left: 3px solid #111;
  border-right: 3px solid #111;
  border-radius: 2px;
  width: 100%;
  pointer-events: none;
  z-index: 1;
}

/* 美化分段竖线（自动生成的div） */
.ol-scale-line-inner > div {
  background: #111 !important;
  width: 2px !important;
  min-width: 2px !important;
  border-radius: 1px;
  z-index: 2;
}

/* 显示数字，字体为黑色 */
.ol-scale-line-inner > span {
  color: #111 !important;
  font-size: 16px !important;
  font-weight: bold;
  left: 0;
  top: -22px;
  position: absolute;
  z-index: 3;
}
</style>