
import TileLayer from 'ol/layer/Tile.js'
import XYZ from 'ol/source/XYZ.js'
import TileImage from 'ol/source/TileImage.js'
import TileGrid from 'ol/tilegrid/TileGrid.js'

/**
 * Create and return the base map layers
 * @returns {Object} Object containing the base map layers
 */
export function createBaseLayers() {
  // 天地图
  const tianKey = '569737ea36171685d686b54ce079a49d'
  const tianLayer = new TileLayer({
    properties: { name: 'tian', title: '天地图' },
    visible: true,
    source: new XYZ({
      projection: 'EPSG:4326',
      url: `http://t{0-7}.tianditu.gov.cn/vec_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tianKey}`
    }),
    zIndex: 100,
  })

  //天地图标注
  const tianLayerLabel = new TileLayer({
    properties: { name: 'tianlabel', title: '天地图注记' },
    visible: true,
    source: new XYZ({
      projection: 'EPSG:4326',
      url: `http://t{0-7}.tianditu.gov.cn/cva_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tianKey}`
    }),
    zIndex: 1000,
  })

  // 高德地图
  const gaodeLayer = new TileLayer({
    properties: { name: 'gaode', title: '高德地图' },
    visible: false,
    source: new XYZ({
      url: 'http://webrd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scl=1&style=8&lstyle=7&x={x}&y={y}&z={z}'
    })
  })

  // 百度地图
  let url = 'http://online{0-3}.map.bdimg.com/onlinelabel/?qt=tile&x={x}&y={y}&z={z}&styles=pl&udt=20191119&scaler=1&p=1'
  const resolutions = []
  for (let i = 0; i < 19; i++) resolutions.push(Math.pow(2, 18 - i))
  const tileGrid = new TileGrid({ origin: [0, 0], resolutions })
  const baiduLayer = new TileLayer({
    properties: { name: 'baidu', title: '百度地图' },
    visible: false,
    source: new TileImage({
      projection: 'EPSG:3857',
      tileGrid: tileGrid,
      tileUrlFunction: function(tileCoord, pixelRatio, proj) {
        if (!tileCoord) return ''
        let tempUrl = url
        tempUrl = tempUrl.replace('{x}', tileCoord[1] < 0 ? `M${-tileCoord[1]}` : tileCoord[1])
        tempUrl = tempUrl.replace('{y}', tileCoord[2] < 0 ? `M${tileCoord[2] + 1}` : -(tileCoord[2] + 1))
        tempUrl = tempUrl.replace('{z}', tileCoord[0])
        var match = /\{(\d+)-(\d+)\}/.exec(tempUrl)
        if (match) {
          var delta = parseInt(match[2]) - parseInt(match[1])
          var num = Math.round(Math.random() * delta + parseInt(match[1]))
          tempUrl = tempUrl.replace(match[0], num.toString())
        }
        return tempUrl
      }
    })
  })

  return {
    tianLayer,
    tianLayerLabel,
    gaodeLayer,
    baiduLayer
  }
}

/**
 * Initialize layer references from the map
 * @param {Object} map - OpenLayers Map instance
 * @returns {Object} Object containing layer references
 */
export function initLayerReferences(map) {
  if (!map) return {}

  // 获取地图中已有的图层
  const layers = map.getLayers().getArray()
  console.log("地图中的图层:", layers)

  // 初始化图层引用对象
  const mapLayers = {}
  let currentBase = 'tian'

  // 遍历所有图层，找到对应的底图图层
  layers.forEach(layer => {
    const name = layer.get('name')
    if (name && ['tian', 'tianlabel', 'baidu', 'gaode'].includes(name)) {
      mapLayers[name] = layer
      console.log(`找到图层: ${name}`)

      // 如果图层可见，更新当前选中的底图
      if (layer.getVisible() && name !== 'tianlabel') {
        currentBase = name
      }
    }
  })

  console.log("初始化的图层:", mapLayers)
  console.log("当前选中的底图:", currentBase)

  return { mapLayers, currentBase }
}

/**
 * Set the visibility of base layers
 * @param {Object} mapLayers - Object containing layer references
 * @param {String} type - Type of base layer to make visible
 */
export function setBaseVisible(mapLayers, type) {
  // 遍历所有图层，设置可见性
  Object.entries(mapLayers).forEach(([key, layer]) => {
    // 天地图注记层的可见性与天地图保持一致
    if (key === 'tianlabel') {
      layer.setVisible(type === 'tian')
    } else {
      layer.setVisible(key === type)
    }
  })
}

/**
 * Switch the base map
 * @param {Object} map - OpenLayers Map instance
 * @param {Object} mapLayers - Object containing layer references
 * @param {String} type - Type of base layer to switch to
 * @returns {String} The new base layer type
 */
export function switchBase(map, mapLayers, type) {
  setBaseVisible(mapLayers, type)

  // 触发自定义事件，通知底图已切换
  if (map) {
    // 使用 propertychange 事件通知其他组件底图已更改
    map.getLayers().forEach(layer => {
      if (layer.get('name') === type) {
        // 触发图层属性变化事件，确保包含 key 和 target 属性
        const event = {
          type: 'propertychange',
          key: 'visible',
          target: layer
        }
        layer.dispatchEvent(event)
      }
    })
  }

  // 添加调试信息
  console.log('切换底图为:', type)
  
  return type
}
