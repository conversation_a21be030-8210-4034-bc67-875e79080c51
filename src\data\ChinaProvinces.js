
const chinaProvinces = [
  {
    province: '北京市',
    capital: '北京市',
    longitude: 116.405285,
    latitude: 39.904989,
    description: '中华人民共和国首都，直辖市，国家中心城市，超大城市'
  },
  {
    province: '天津市',
    capital: '天津市',
    longitude: 117.190182,
    latitude: 39.125596,
    description: '直辖市，国家中心城市，超大城市'
  },
  {
    province: '河北省',
    capital: '石家庄市',
    longitude: 114.502461,
    latitude: 38.045474,
    description: '省会城市，特大城市'
  },
  {
    province: '山西省',
    capital: '太原市',
    longitude: 112.549248,
    latitude: 37.857014,
    description: '省会城市，特大城市'
  },
  {
    province: '内蒙古自治区',
    capital: '呼和浩特市',
    longitude: 111.670801,
    latitude: 40.818311,
    description: '自治区首府，特大城市'
  },
  {
    province: '辽宁省',
    capital: '沈阳市',
    longitude: 123.429096,
    latitude: 41.796767,
    description: '省会城市，特大城市'
  },
  {
    province: '吉林省',
    capital: '长春市',
    longitude: 125.3245,
    latitude: 43.886841,
    description: '省会城市，特大城市'
  },
  {
    province: '黑龙江省',
    capital: '哈尔滨市',
    longitude: 126.642464,
    latitude: 45.756967,
    description: '省会城市，特大城市'
  },
  {
    province: '上海市',
    capital: '上海市',
    longitude: 121.472644,
    latitude: 31.231706,
    description: '直辖市，国家中心城市，超大城市'
  },
  {
    province: '江苏省',
    capital: '南京市',
    longitude: 118.767413,
    latitude: 32.041544,
    description: '省会城市，特大城市'
  },
  {
    province: '浙江省',
    capital: '杭州市',
    longitude: 120.153576,
    latitude: 30.287459,
    description: '省会城市，特大城市'
  },
  {
    province: '安徽省',
    capital: '合肥市',
    longitude: 117.283042,
    latitude: 31.86119,
    description: '省会城市，特大城市'
  },
  {
    province: '福建省',
    capital: '福州市',
    longitude: 119.306239,
    latitude: 26.075302,
    description: '省会城市，特大城市'
  },
  {
    province: '江西省',
    capital: '南昌市',
    longitude: 115.892151,
    latitude: 28.676493,
    description: '省会城市，特大城市'
  },
  {
    province: '山东省',
    capital: '济南市',
    longitude: 117.000923,
    latitude: 36.675807,
    description: '省会城市，特大城市'
  },
  {
    province: '河南省',
    capital: '郑州市',
    longitude: 113.665412,
    latitude: 34.757975,
    description: '省会城市，特大城市'
  },
  {
    province: '湖北省',
    capital: '武汉市',
    longitude: 114.298572,
    latitude: 30.584355,
    description: '省会城市，国家中心城市，超大城市'
  },
  {
    province: '湖南省',
    capital: '长沙市',
    longitude: 112.982279,
    latitude: 28.19409,
    description: '省会城市，特大城市'
  },
  {
    province: '广东省',
    capital: '广州市',
    longitude: 113.280637,
    latitude: 23.125178,
    description: '省会城市，国家中心城市，超大城市'
  },
  {
    province: '广西壮族自治区',
    capital: '南宁市',
    longitude: 108.320004,
    latitude: 22.82402,
    description: '自治区首府，特大城市'
  },
  {
    province: '海南省',
    capital: '海口市',
    longitude: 110.33119,
    latitude: 20.031971,
    description: '省会城市，特大城市'
  },
  {
    province: '重庆市',
    capital: '重庆市',
    longitude: 106.504962,
    latitude: 29.533155,
    description: '直辖市，国家中心城市，超大城市'
  },
  {
    province: '四川省',
    capital: '成都市',
    longitude: 104.065735,
    latitude: 30.659462,
    description: '省会城市，国家中心城市，超大城市'
  },
  {
    province: '贵州省',
    capital: '贵阳市',
    longitude: 106.713478,
    latitude: 26.578343,
    description: '省会城市，特大城市'
  },
  {
    province: '云南省',
    capital: '昆明市',
    longitude: 102.712251,
    latitude: 25.040609,
    description: '省会城市，特大城市'
  },
  {
    province: '西藏自治区',
    capital: '拉萨市',
    longitude: 91.132212,
    latitude: 29.660361,
    description: '自治区首府，高原城市'
  },
  {
    province: '陕西省',
    capital: '西安市',
    longitude: 108.948024,
    latitude: 34.263161,
    description: '省会城市，国家中心城市，特大城市'
  },
  {
    province: '甘肃省',
    capital: '兰州市',
    longitude: 103.823557,
    latitude: 36.058039,
    description: '省会城市，特大城市'
  },
  {
    province: '青海省',
    capital: '西宁市',
    longitude: 101.778916,
    latitude: 36.623178,
    description: '省会城市，高原城市'
  },
  {
    province: '宁夏回族自治区',
    capital: '银川市',
    longitude: 106.278179,
    latitude: 38.46637,
    description: '自治区首府，特大城市'
  },
  {
    province: '新疆维吾尔自治区',
    capital: '乌鲁木齐市',
    longitude: 87.617733,
    latitude: 43.792818,
    description: '自治区首府，特大城市'
  },
  {
    province: '台湾省',
    capital: '台北市',
    longitude: 121.520076,
    latitude: 25.030724,
    description: '省会城市，特大城市'
  },
  {
    province: '香港特别行政区',
    capital: '香港',
    longitude: 114.173355,
    latitude: 22.320048,
    description: '特别行政区，国际金融中心'
  },
  {
    province: '澳门特别行政区',
    capital: '澳门',
    longitude: 113.54909,
    latitude: 22.198951,
    description: '特别行政区，国际旅游城市'
  }
];

/**
 * 获取所有省份和省会数据
 * @returns {Array} 省份和省会数据数组
 */
export function getAllProvinces() {
  return chinaProvinces;
}

/**
 * 根据省份名称查找省份数据
 * @param {String} provinceName - 省份名称
 * @returns {Object|null} 省份数据对象或null
 */
export function findProvinceByName(provinceName) {
  return chinaProvinces.find(item => 
    item.province.includes(provinceName) || 
    item.capital.includes(provinceName)
  ) || null;
}

/**
 * 获取所有省份名称列表
 * @returns {Array} 省份名称数组
 */
export function getProvinceNames() {
  return chinaProvinces.map(item => item.province);
}

/**
 * 获取所有省会名称列表
 * @returns {Array} 省会名称数组
 */
export function getCapitalNames() {
  return chinaProvinces.map(item => item.capital);
}

export default chinaProvinces;
