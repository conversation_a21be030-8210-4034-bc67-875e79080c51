<script setup>
import { ref, defineProps, defineEmits, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  map: Object,
  inNavbar: { type: Boolean, default: false },
  enabled: { type: Boolean, default: false }
})

const emit = defineEmits(['update:enabled'])

const view = ref(null)
const initialZoom = ref(null)
const initialCenter = ref(null)
const initialRotation = ref(null)
const showDropdown = ref(false)

// 工具列表
const tools = [
  {
    key: 'zoomIn',
    label: '放大一级',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"></path>
    </svg>`,
    action: zoomIn
  },
  {
    key: 'zoomOut',
    label: '缩小一级',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M19 13H5v-2h14v2z"></path>
    </svg>`,
    action: zoomOut
  },
  {
    key: 'moveToWuhan',
    label: '移动到武汉',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M15 11V5l-3-3-3 3v2H3v14h18V11h-6zm-8 8H5v-2h2v2zm0-4H5v-2h2v2zm0-4H5V9h2v2zm6 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V9h2v2zm0-4h-2V5h2v2zm6 12h-2v-2h2v2zm0-4h-2v-2h2v2z"></path>
    </svg>`,
    action: moveToWuhan
  },
  {
    key: 'reset',
    label: '复位',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
      <path d="M17.65 6.35A7.958 7.958 0 0 0 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08A5.99 5.99 0 0 1 12 18c-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"></path>
    </svg>`,
    action: resetMap
  }
]

// 初始化控件
const initMapControls = () => {
  if (!props.map) return

  view.value = props.map.getView()

  initialZoom.value = view.value.getZoom()
  initialCenter.value = view.value.getCenter()
  initialRotation.value = view.value.getRotation()
}

// 切换下拉菜单
const toggleDropdown = () => {
  if (props.inNavbar) {
    emit('update:enabled', !props.enabled)
  } else {
    showDropdown.value = !showDropdown.value
  }

  console.log('地图操作功能状态:', props.inNavbar ? (props.enabled ? '关闭' : '开启') : (showDropdown.value ? '开启' : '关闭'))
}

// 执行工具操作
const executeTool = (tool) => {
  if (tool.action) {
    tool.action()
  }

  if (!props.inNavbar) {
    showDropdown.value = false
  }
}

// 放大
const zoomIn = () => {
  if (!view.value) return

  const curZoom = view.value.getZoom()

  view.value.setZoom(curZoom + 1)
}

// 缩小
const zoomOut = () => {
  if (!view.value) return

  const curZoom = view.value.getZoom()

  view.value.setZoom(curZoom - 1)
}

// 移动到武汉
const moveToWuhan = () => {
  if (!view.value) return

  view.value.setCenter([12727039.383734727, 3579066.6894065146])
  view.value.setZoom(12)
}

// 复位
const resetMap = () => {
  if (!view.value) return

  view.value.setZoom(initialZoom.value)
  view.value.setCenter(initialCenter.value)
  view.value.setRotation(initialRotation.value)
}

// 点击外部关闭下拉菜单
function handleClickOutside(event) {
  if (props.inNavbar) return

  const mapControls = document.querySelector('.map-controls-container')
  if (mapControls && !mapControls.contains(event.target)) {
    showDropdown.value = false
  }
}

// 添加和移除点击事件监听器
onMounted(() => {
  if (!props.inNavbar) {
    document.addEventListener('click', handleClickOutside)
  }
})

onUnmounted(() => {
  if (!props.inNavbar) {
    document.removeEventListener('click', handleClickOutside)
  }
})

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    initMapControls()
  }
}, { immediate: true })

defineExpose({
  zoomIn,
  zoomOut,
  moveToWuhan,
  resetMap,
  toggleDropdown
})
</script>

<template>
  <!-- 独立模式 -->
  <div v-if="!inNavbar" class="map-controls-container">
    <div class="map-controls-toggle" @click.stop="toggleDropdown">
      <span>地图控制</span>
      <div class="triangle" :class="{ 'triangle-up': showDropdown }"></div>
    </div>
    <div class="map-controls-dropdown" v-if="showDropdown">
      <div
        v-for="tool in tools"
        :key="tool.key"
        class="map-controls-item"
        @click="executeTool(tool)"
      >
        <span class="tool-icon" v-html="tool.icon"></span>
        {{ tool.label }}
      </div>
    </div>
  </div>

  <!-- 导航栏模式 - 只显示下拉菜单 -->
  <div v-else-if="enabled" class="map-ops-dropdown">
    <div
      v-for="tool in tools"
      :key="tool.key"
      class="dropdown-item"
      @click="executeTool(tool)"
    >
      <span class="dropdown-icon" v-html="tool.icon"></span> {{ tool.label }}
    </div>
  </div>
</template>

<style>
/* 独立模式样式 */
.map-controls-container {
  position: absolute;
  top: 10px;
  left: 440px;
  z-index: 9000;
  font-size: 15px;
  color: #333;
  min-width: 120px;
}

.map-controls-toggle {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: none;
  border-radius: 10px;
  padding: 8px 18px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  user-select: none;
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
  transition: background 0.2s;
}
.map-controls-toggle:hover {
  background-color: #f5f7fa;
}
.triangle {
  width: 0;
  height: 0;
  margin-left: 8px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #1890ff;
  transition: transform 0.2s ease;
}
.triangle-up {
  transform: rotate(180deg);
}
.map-controls-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 180px;
  background-color: #fff;
  border-radius: 10px;
  margin-top: 4px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  overflow: hidden;
  z-index: 5000;
  border: none;
}
.map-controls-item {
  padding: 10px 18px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
  display: flex;
  align-items: center;
  font-size: 15px;
  border-radius: 0;
}
.tool-icon {
  margin-right: 10px;
  font-size: 18px;
}
.map-controls-item:hover {
  background-color: #e6f7ff;
  color: #1890ff;
}

/* 导航栏模式样式 */
.map-ops-dropdown {
  position: absolute;
  top: 60px;
  right: 320px;
  width: 220px;
  background-color: #333333;
  border: 1px solid #444444;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  z-index: 10001;
  overflow: hidden;
}

.dropdown-item {
  padding: 10px 15px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #ffffff;
}

.dropdown-item:hover {
  background-color: #444444;
}

.dropdown-icon {
  margin-right: 10px;
  font-size: 16px;
}
</style>
