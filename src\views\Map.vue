<script setup>
import Map from 'ol/Map.js'
import View from 'ol/View.js'
import TileLayer from 'ol/layer/Tile.js'
import XYZ from 'ol/source/XYZ.js'
import TileImage from 'ol/source/TileImage.js'
import TileGrid from 'ol/tilegrid/TileGrid.js'
import ScaleLine from 'ol/control/ScaleLine.js'

import { onMounted, ref, onUnmounted } from 'vue'
import MapSwitcher from './control/MapSwitcher.vue'
import MousePositionComponent from './control/MousePosition.vue'
import ZoomComponent from './control/Zoom.vue'
import OverviewComponent from './control/overview.vue'
import ScaleLineComponent from './control/ScaleLine.vue'
import globalLayerManager from '../utils/GlobalLayerManager.js'

// 定义外部参数
const props = defineProps({
  viewConf: { type: Object, default: () => ({}) },
  defLyrs: { type: Array, default: () => ['vec_c'] },
  defaultControl: { type: Array, default: () => [] }
})


const emit = defineEmits(['created'])
const mapInstance = ref(null)
const initialZoom = ref(null)
const initialCenter = ref(null)
const initialRotation = ref(null)
const mousePositionRef = ref(null)
const mapSwitcherRef = ref(null)

// 窗口大小变化时更新地图尺寸
const updateMapSize = () => {
  if (mapInstance.value) {
    mapInstance.value.updateSize()
  }
}

// 地图操作函数
const zoomIn = () => {
  if (!mapInstance.value) return
  const view = mapInstance.value.getView()
  const curZoom = view.getZoom()
  view.setZoom(curZoom + 1)
}

const zoomOut = () => {
  if (!mapInstance.value) return
  const view = mapInstance.value.getView()
  const curZoom = view.getZoom()
  view.setZoom(curZoom - 1)
}

const moveToWuhan = () => {
  if (!mapInstance.value) return
  const view = mapInstance.value.getView()
  view.setCenter([12727039.383734727, 3579066.6894065146])
  view.setZoom(12)
}

const resetMap = () => {
  if (!mapInstance.value || !initialZoom.value) return
  const view = mapInstance.value.getView()
  view.setZoom(initialZoom.value)
  view.setCenter(initialCenter.value)
  view.setRotation(initialRotation.value)
}

// 组件挂载后创建地图
onMounted(() => {
  const viewOpts = Object.assign({
    projection: 'EPSG:3857',
    center: [12758612.973162018, 3562849.0216611675],
    zoom: 17.5
  }, props.viewConf)

  // 天地图
  const tianKey = '569737ea36171685d686b54ce079a49d'
  const tianLayer = new TileLayer({
    properties: { name: 'tian', title: '天地图' },
    visible: true,
    source: new XYZ({
      projection: 'EPSG:4326',
      url: `http://t{0-7}.tianditu.gov.cn/vec_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tianKey}`
    }),
    zIndex: 100,
  })

  //天地图标注
  const tianLayerLabel = new TileLayer({
    properties: { name: 'tianlabel', title: '天地图注记' },
    visible: true,
    source: new XYZ({
      projection: 'EPSG:4326',
      url: `http://t{0-7}.tianditu.gov.cn/cva_c/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=c&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tianKey}`
    }),
    zIndex: 1000,
  })

  // 高德地图
  const gaodeLayer = new TileLayer({
    properties: { name: 'gaode', title: '高德地图' },
    visible: false,
    source: new XYZ({
      url: 'http://webrd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&scl=1&style=8&lstyle=7&x={x}&y={y}&z={z}'
    })
  })

  // 百度地图
  let url = 'http://online{0-3}.map.bdimg.com/onlinelabel/?qt=tile&x={x}&y={y}&z={z}&styles=pl&udt=20191119&scaler=1&p=1'
  const resolutions = []
  for (let i = 0; i < 19; i++) resolutions.push(Math.pow(2, 18 - i))
  const tileGrid = new TileGrid({ origin: [0, 0], resolutions })
  const baiduLayer = new TileLayer({
    properties: { name: 'baidu', title: '百度地图' },
    visible: false,
    source: new TileImage({
      projection: 'EPSG:3857',
      tileGrid: tileGrid,
      tileUrlFunction: function(tileCoord, pixelRatio, proj) {
        if (!tileCoord) return ''
        let tempUrl = url
        tempUrl = tempUrl.replace('{x}', tileCoord[1] < 0 ? `M${-tileCoord[1]}` : tileCoord[1])
        tempUrl = tempUrl.replace('{y}', tileCoord[2] < 0 ? `M${tileCoord[2] + 1}` : -(tileCoord[2] + 1))
        tempUrl = tempUrl.replace('{z}', tileCoord[0])
        var match = /\{(\d+)-(\d+)\}/.exec(tempUrl)
        if (match) {
          var delta = parseInt(match[2]) - parseInt(match[1])
          var num = Math.round(Math.random() * delta + parseInt(match[1]))
          tempUrl = tempUrl.replace(match[0], num.toString())
        }
        return tempUrl
      }
    })
  })

  const map = new Map({
    target: 'mapDom',
    view: new View(viewOpts),
    layers: [tianLayerLabel,tianLayer, gaodeLayer, baiduLayer],
    controls: props.defaultControl
  })

  mapInstance.value = map

  const view = map.getView()
  initialZoom.value = view.getZoom()
  initialCenter.value = view.getCenter()
  initialRotation.value = view.getRotation()

  // 初始化全局图层管理器
  globalLayerManager.init(map)

  emit('created', map)

  window.addEventListener('resize', updateMapSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateMapSize)
})

// 定位功能控制
const togglePositionTool = () => {
  if (mousePositionRef.value) {
    mousePositionRef.value.toggleCoordinatesDisplay()

    // 获取当前状态
    const isEnabled = mousePositionRef.value.showCoordinatesEnabled

    if (!isEnabled) {
      mousePositionRef.value.removeCoordinateTooltip()
    }

    return isEnabled
  }

  return false
}

defineExpose({
  togglePositionTool,
  mousePositionRef,
  mapSwitcherRef,
  mapInstance,
  zoomIn,
  zoomOut,
  moveToWuhan,
  resetMap
})

</script>

<template>
  <div id="mapDom" class="map"></div>
  <ScaleLineComponent :map="mapInstance" v-if="mapInstance" />
  <div style="display: none;">
    <MapSwitcher ref="mapSwitcherRef" :map="mapInstance" v-if="mapInstance" />
    <MousePositionComponent ref="mousePositionRef" :map="mapInstance" v-if="mapInstance" />
    <ZoomComponent :map="mapInstance" v-if="mapInstance" />
    <OverviewComponent :map="mapInstance" v-if="mapInstance" />
  </div>
</template>

<style>
.map {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: 0;
  padding: 0;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: hidden;
}

</style>