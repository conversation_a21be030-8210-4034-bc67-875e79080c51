<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import Draw from 'ol/interaction/Draw.js'
import Overlay from 'ol/Overlay.js'
import { Circle as CircleStyle, Fill, Stroke, Style, Text } from 'ol/style.js'
import { LineString, Polygon, Point } from 'ol/geom.js'
import { Vector as VectorSource } from 'ol/source.js'
import { Vector as VectorLayer } from 'ol/layer.js'
import { unByKey } from 'ol/Observable.js'
import { getArea, getLength } from 'ol/sphere.js'
import { transform } from 'ol/proj.js'
import Feature from 'ol/Feature.js'
import { toStringHDMS } from 'ol/coordinate.js'

const props = defineProps({
  map: Object
})

const measureType = ref('distance')
const measuring = ref(false)
const source = ref(null)
const vector = ref(null)
const draw = ref(null)
const helpTooltipElement = ref(null)
const helpTooltip = ref(null)
const measureTooltipElement = ref(null)
const measureTooltip = ref(null)
const measureTooltips = ref([])
const measureTooltipElements = ref([])
const listener = ref(null)

const initMeasure = () => {
  if (!props.map) return

  // 创建矢量源和图层
  source.value = new VectorSource()
  vector.value = new VectorLayer({
    source: source.value,
    style: (feature) => {
      const geometry = feature.getGeometry()
      const geometryType = geometry.getType()

      // 基本样式
      const styles = [
        new Style({
          fill: new Fill({
            color: 'rgba(24, 144, 255, 0.2)'
          }),
          stroke: new Stroke({
            color: '#1890ff',
            width: 2
          }),
          image: new CircleStyle({
            radius: 5,
            stroke: new Stroke({
              color: '#1890ff'
            }),
            fill: new Fill({
              color: 'rgba(255, 255, 255, 0.8)'
            })
          })
        })
      ]

      // 为线段添加顶点样式
      if (geometryType === 'LineString') {
        const coordinates = geometry.getCoordinates()

        coordinates.forEach((coordinate, index) => {
          if (index === 0 || index === coordinates.length - 1) {
            styles.push(
              new Style({
                geometry: new Point(coordinate),
                image: new CircleStyle({
                  radius: 6,
                  fill: new Fill({
                    color: index === 0 ? '#34c759' : '#ff3b30'
                  }),
                  stroke: new Stroke({
                    color: 'white',
                    width: 2
                  })
                })
              })
            )
          }
        })
      }

      // 为多边形添加中心点标记
      if (geometryType === 'Polygon') {
        const center = geometry.getInteriorPoint().getCoordinates()
        styles.push(
          new Style({
            geometry: new Point(center),
            image: new CircleStyle({
              radius: 6,
              fill: new Fill({
                color: '#5856d6'
              }),
              stroke: new Stroke({
                color: 'white',
                width: 2
              })
            })
          })
        )
      }

      return styles
    },
    zIndex: 1000
  })

  props.map.addLayer(vector.value)

  createHelpTooltip()
}

// 创建帮助提示框
const createHelpTooltip = () => {
  if (helpTooltipElement.value) {
    helpTooltipElement.value.parentNode.removeChild(helpTooltipElement.value)
  }

  helpTooltipElement.value = document.createElement('div')
  helpTooltipElement.value.className = 'ol-tooltip'
  helpTooltip.value = new Overlay({
    element: helpTooltipElement.value,
    offset: [15, 0],
    positioning: 'center-left'
  })

  props.map.addOverlay(helpTooltip.value)
}

// 创建测量提示框
const createMeasureTooltip = () => {
  const element = document.createElement('div')
  element.className = 'ol-tooltip ol-tooltip-measure'

  const tooltip = new Overlay({
    element: element,
    offset: [0, -15],
    positioning: 'bottom-center',
    stopEvent: false,
    insertFirst: false
  })

  props.map.addOverlay(tooltip)

  measureTooltipElements.value.push(element)
  measureTooltips.value.push(tooltip)

  measureTooltipElement.value = element
  measureTooltip.value = tooltip
}

// 格式化长度
const formatLength = (line) => {
  const length = getLength(line)
  let output

  if (length > 1000) {
    output = (Math.round(length / 1000 * 1000) / 1000) + ' km'
  } else {
    output = (Math.round(length * 100) / 100) + ' m'
  }

  return output
}

// 格式化面积
const formatArea = (polygon) => {
  const area = getArea(polygon)
  let output

  if (area > 1000000) {
    output = (Math.round(area / 1000000 * 1000) / 1000) + ' km²'
  } else if (area > 10000) {
    output = (Math.round(area / 10000 * 100) / 100) + ' 公顷'
  } else {
    output = (Math.round(area * 100) / 100) + ' m²'
  }

  return output
}

// 格式化坐标
const formatCoordinate = (coordinate) => {
  return toStringHDMS(transform(coordinate, 'EPSG:3857', 'EPSG:4326'))
}

// 开始测量
const startMeasure = (type) => {
  if (!props.map) return

  if (measuring.value) {
    stopMeasure()
  }

  measureType.value = type
  measuring.value = true

  createMeasureTooltip()

  // 创建绘图交互
  const drawType = type === 'area' ? 'Polygon' : 'LineString'
  draw.value = new Draw({
    source: source.value,
    type: drawType,
    freehand: false,
    // 设置绘制样式
    style: new Style({
      fill: new Fill({
        color: 'rgba(24, 144, 255, 0.2)'
      }),
      stroke: new Stroke({
        color: '#1890ff',
        lineDash: [6, 6],
        width: 2
      }),
      image: new CircleStyle({
        radius: 5,
        stroke: new Stroke({
          color: '#1890ff',
          width: 1.5
        }),
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.8)'
        })
      })
    })
  })

  props.map.addInteraction(draw.value)

  // 添加绘图开始事件
  draw.value.on('drawstart', (evt) => {
    createMeasureTooltip()

    const sketch = evt.feature

    let tooltipCoord = null

    listener.value = sketch.getGeometry().on('change', (e) => {
      const geom = e.target
      let output = ''

      if (type === 'area') {
        output = formatArea(geom)
        tooltipCoord = geom.getInteriorPoint().getCoordinates()

        // 显示多边形的顶点坐标
        const coordinates = geom.getCoordinates()[0]
        if (coordinates && coordinates.length > 2) {
          // 清除之前的顶点标记
          measureTooltips.value.forEach(tooltip => {
            if (tooltip.getElement().className.includes('ol-tooltip-vertex')) {
              props.map.removeOverlay(tooltip)
              const index = measureTooltips.value.indexOf(tooltip)
              if (index > -1) {
                measureTooltips.value.splice(index, 1)
                measureTooltipElements.value.splice(index, 1)
              }
            }
          })

          for (let i = 0; i < coordinates.length - 1; i++) {
            // 创建顶点坐标提示框
            const vertexElement = document.createElement('div')
            vertexElement.className = 'ol-tooltip ol-tooltip-vertex'
            vertexElement.innerHTML = `点 ${i+1}`

            const vertexTooltip = new Overlay({
              element: vertexElement,
              offset: [0, -15],
              positioning: 'bottom-center',
              stopEvent: false,
              insertFirst: false
            })

            vertexTooltip.setPosition(coordinates[i])
            props.map.addOverlay(vertexTooltip)

            measureTooltipElements.value.push(vertexElement)
            measureTooltips.value.push(vertexTooltip)
          }
        }
      } else if (type === 'distance') {
        output = formatLength(geom)
        tooltipCoord = geom.getLastCoordinate()

        // 显示每段距离
        const coordinates = geom.getCoordinates()
        if (coordinates.length > 1) {
          // 清除之前的线段标记
          measureTooltips.value.forEach(tooltip => {
            if (tooltip.getElement().className.includes('ol-tooltip-segment')) {
              props.map.removeOverlay(tooltip)
              const index = measureTooltips.value.indexOf(tooltip)
              if (index > -1) {
                measureTooltips.value.splice(index, 1)
                measureTooltipElements.value.splice(index, 1)
              }
            }
          })

          // 清除之前的顶点标记
          measureTooltips.value.forEach(tooltip => {
            if (tooltip.getElement().className.includes('ol-tooltip-vertex')) {
              props.map.removeOverlay(tooltip)
              const index = measureTooltips.value.indexOf(tooltip)
              if (index > -1) {
                measureTooltips.value.splice(index, 1)
                measureTooltipElements.value.splice(index, 1)
              }
            }
          })

          // 为每个点添加标记
          for (let i = 0; i < coordinates.length; i++) {
            // 创建顶点坐标提示框
            const vertexElement = document.createElement('div')
            vertexElement.className = 'ol-tooltip ol-tooltip-vertex'
            vertexElement.innerHTML = `点 ${i+1}`

            const vertexTooltip = new Overlay({
              element: vertexElement,
              offset: [10, 0],
              positioning: 'center-left',
              stopEvent: false,
              insertFirst: false
            })

            vertexTooltip.setPosition(coordinates[i])
            props.map.addOverlay(vertexTooltip)

            measureTooltipElements.value.push(vertexElement)
            measureTooltips.value.push(vertexTooltip)
          }

          // 为每段线添加距离标记
          for (let i = 1; i < coordinates.length; i++) {
            const segment = new LineString([coordinates[i-1], coordinates[i]])
            const segmentText = formatLength(segment)

            // 创建线段测量提示框
            const segElement = document.createElement('div')
            segElement.className = 'ol-tooltip ol-tooltip-segment'
            segElement.innerHTML = segmentText

            const segTooltip = new Overlay({
              element: segElement,
              offset: [0, -15],
              positioning: 'bottom-center',
              stopEvent: false,
              insertFirst: false
            })

            // 计算线段中点
            const midpoint = [
              (coordinates[i-1][0] + coordinates[i][0]) / 2,
              (coordinates[i-1][1] + coordinates[i][1]) / 2
            ]

            segTooltip.setPosition(midpoint)
            props.map.addOverlay(segTooltip)

            measureTooltipElements.value.push(segElement)
            measureTooltips.value.push(segTooltip)
          }

          output = `总长度: ${formatLength(geom)}`
        }
      }

      measureTooltipElement.value.innerHTML = output
      measureTooltip.value.setPosition(tooltipCoord)
    })
  })

  // 添加绘图结束事件
  draw.value.on('drawend', (evt) => {
    measureTooltipElement.value.className = 'ol-tooltip ol-tooltip-static'
    measureTooltip.value.setOffset([0, -7])

    const geometry = evt.feature.getGeometry()

    if (type === 'area') {
      const area = getArea(geometry)
      const formattedArea = formatArea(geometry)

      console.log(`测量面积: ${formattedArea} (${area} 平方米)`)

      const resultElement = document.createElement('div')
      resultElement.className = 'ol-tooltip ol-tooltip-result'
      resultElement.innerHTML = `总面积: ${formattedArea}`

      const resultTooltip = new Overlay({
        element: resultElement,
        offset: [0, 0],
        positioning: 'center-center',
        stopEvent: false
      })

      // 设置位置为多边形中心
      const center = geometry.getInteriorPoint().getCoordinates()
      resultTooltip.setPosition(center)
      props.map.addOverlay(resultTooltip)

      measureTooltipElements.value.push(resultElement)
      measureTooltips.value.push(resultTooltip)
    } else if (type === 'distance') {
      const length = getLength(geometry)
      const formattedLength = formatLength(geometry)

      console.log(`测量距离: ${formattedLength} (${length} 米)`)

      // 添加总距离结果标记
      const resultElement = document.createElement('div')
      resultElement.className = 'ol-tooltip ol-tooltip-result'
      resultElement.innerHTML = `总长度: ${formattedLength}`

      const resultTooltip = new Overlay({
        element: resultElement,
        offset: [0, -15],
        positioning: 'bottom-center',
        stopEvent: false
      })

      // 设置位置为线的最后一个点
      const coordinates = geometry.getCoordinates()
      const lastPoint = coordinates[coordinates.length - 1]
      resultTooltip.setPosition(lastPoint)
      props.map.addOverlay(resultTooltip)

      measureTooltipElements.value.push(resultElement)
      measureTooltips.value.push(resultTooltip)
    }

    measureTooltipElement.value = null

    unByKey(listener.value)
    listener.value = null

    createHelpTooltip()

    measuring.value = false

    if (draw.value) {
      props.map.removeInteraction(draw.value)
      draw.value = null
    }
  })

  // 添加鼠标移动事件
  const pointerMoveHandler = (evt) => {
    if (evt.dragging || !measuring.value) {
      return
    }

    let helpMsg = '点击地图添加第一个点'

    if (draw.value && draw.value.sketchFeature_) {
      const sketchGeom = draw.value.sketchFeature_.getGeometry()

      if (type === 'area') {
        const coords = sketchGeom.getCoordinates()[0]
        if (coords.length > 2) {
          helpMsg = `已添加 ${coords.length - 1} 个点，双击结束测量`
        } else {
          helpMsg = '点击继续添加多边形顶点'
        }
      } else {
        const coords = sketchGeom.getCoordinates()
        if (coords.length > 1) {
          helpMsg = `已添加 ${coords.length} 个点，双击结束测量`
        } else {
          helpMsg = '点击添加下一个点'
        }
      }
    }

    if (helpTooltipElement.value) {
      helpTooltipElement.value.innerHTML = helpMsg
      helpTooltip.value.setPosition(evt.coordinate)
      helpTooltipElement.value.classList.remove('hidden')
    }
  }

  const moveListener = props.map.on('pointermove', pointerMoveHandler)
}

// 停止测量
const stopMeasure = () => {
  if (!props.map) return

  measuring.value = false

  if (draw.value) {
    props.map.removeInteraction(draw.value)
    draw.value = null
  }

  // 移除帮助提示框
  if (helpTooltip.value) {
    props.map.removeOverlay(helpTooltip.value)
    helpTooltip.value = null
  }

  if (helpTooltipElement.value) {
    if (helpTooltipElement.value.parentNode) {
      helpTooltipElement.value.parentNode.removeChild(helpTooltipElement.value)
    }
    helpTooltipElement.value = null
  }

  if (listener.value) {
    unByKey(listener.value)
    listener.value = null
  }

  // 移除临时测量提示框
  measureTooltips.value.forEach(tooltip => {
    if (tooltip.getElement().className.includes('ol-tooltip-measure') &&
        !tooltip.getElement().className.includes('ol-tooltip-static')) {
      props.map.removeOverlay(tooltip)
      const index = measureTooltips.value.indexOf(tooltip)
      if (index > -1) {
        measureTooltips.value.splice(index, 1)
        measureTooltipElements.value.splice(index, 1)
      }
    }
  })
}

// 清除测量结果
const clearMeasurement = () => {
  if (!props.map) return

  if (measuring.value) {
    stopMeasure()
  }

  if (source.value) {
    source.value.clear()
  }

  // 移除所有测量提示框
  measureTooltips.value.forEach(tooltip => {
    props.map.removeOverlay(tooltip)
  })

  measureTooltipElements.value.forEach(element => {
    if (element.parentNode) {
      element.parentNode.removeChild(element)
    }
  })

  measureTooltips.value = []
  measureTooltipElements.value = []
  measureTooltip.value = null
  measureTooltipElement.value = null

  // 移除帮助提示框
  if (helpTooltip.value) {
    props.map.removeOverlay(helpTooltip.value)
    helpTooltip.value = null
  }

  if (helpTooltipElement.value) {
    if (helpTooltipElement.value.parentNode) {
      helpTooltipElement.value.parentNode.removeChild(helpTooltipElement.value)
    }
    helpTooltipElement.value = null
  }

  createHelpTooltip()

  // 显示清除成功的提示
  const successMsg = document.createElement('div')
  successMsg.className = 'ol-tooltip ol-tooltip-success'
  successMsg.innerHTML = '测量已清除'

  const successTooltip = new Overlay({
    element: successMsg,
    offset: [0, 0],
    positioning: 'center-center',
    stopEvent: false
  })

  // 获取地图中心点
  const center = props.map.getView().getCenter()
  successTooltip.setPosition(center)
  props.map.addOverlay(successTooltip)

  // 2秒后自动移除提示
  setTimeout(() => {
    props.map.removeOverlay(successTooltip)
    if (successMsg.parentNode) {
      successMsg.parentNode.removeChild(successMsg)
    }
  }, 1500)
}

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    initMeasure()
  }
}, { immediate: true })

// 组件卸载时清理
onUnmounted(() => {
  stopMeasure()
  clearMeasurement()

  if (props.map && vector.value) {
    props.map.removeLayer(vector.value)
  }
})

defineExpose({
  startMeasure,
  stopMeasure,
  clearMeasurement
})
</script>

<template>
  <div class="gauging-tools">
    <div class="gauging-buttons">
      <button
        class="gauging-button"
        :class="{ active: measuring && measureType === 'distance' }"
        @click="startMeasure('distance')"
        title="测量地图上的距离"
      >
        <span class="button-icon">📏</span>
        测量距离
      </button>
      <button
        class="gauging-button"
        :class="{ active: measuring && measureType === 'area' }"
        @click="startMeasure('area')"
        title="测量地图上的面积"
      >
        <span class="button-icon">📐</span>
        测量面积
      </button>
      <button
        class="gauging-button clear-button"
        @click="clearMeasurement"
        title="清除所有测量结果"
      >
        <span class="button-icon">🗑️</span>
        清除测量
      </button>
    </div>
    <div class="gauging-help" v-if="measuring">
      <div class="help-text">
        <p>{{ measureType === 'distance' ? '点击地图添加测量点，双击结束测量' : '点击地图添加多边形顶点，双击结束测量' }}</p>
      </div>
    </div>
  </div>
</template>

<style>
.gauging-tools {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.gauging-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.gauging-button {
  display: flex;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.button-icon {
  margin-right: 8px;
  font-size: 16px;
}

.gauging-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.gauging-button.active {
  background-color: #1890ff;
  color: white;
}

.gauging-button.clear-button {
  background-color: #f5f5f5;
  color: #333;
}

.gauging-button.clear-button:hover {
  background-color: #ff4d4f;
  color: white;
}

.gauging-help {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  max-width: 200px;
}

.help-text p {
  margin: 0;
  line-height: 1.4;
}

/* 提示框样式 */
.ol-tooltip {
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  color: #333;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.ol-tooltip-measure {
  opacity: 1;
  font-weight: bold;
  background-color: rgba(24, 144, 255, 0.8);
  color: white;
}

.ol-tooltip-static {
  background-color: rgba(24, 144, 255, 0.8);
  color: white;
  border: 1px solid white;
}

.ol-tooltip-segment {
  background-color: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 1px solid #1890ff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
}

.ol-tooltip-vertex {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  border: 1px solid #ff9500;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 8px;
}

.ol-tooltip-success {
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
  border: 2px solid white;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: fadeInOut 1.5s ease-in-out;
  pointer-events: none;
}


@keyframes fadeInOut {
  0% { opacity: 0; transform: scale(0.8); }
  20% { opacity: 1; transform: scale(1.1); }
  30% { opacity: 1; transform: scale(1); }
  80% { opacity: 1; }
  100% { opacity: 0; }
}

.ol-tooltip.hidden {
  display: none;
}
</style>