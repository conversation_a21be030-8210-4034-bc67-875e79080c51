<script setup>
import { ref, defineProps, watch, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  map: Object
})

const baseTypes = [
  {
    key: 'tian',
    label: '天地图',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22" fill="#1890ff">
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"></path>
    </svg>`
  },
  {
    key: 'baidu',
    label: '百度地图',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22" fill="#3470ff">
      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"></path>
    </svg>`
  },
  {
    key: 'gaode',
    label: '高德地图',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="22" height="22" fill="#fa8c16">
      <path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"></path>
    </svg>`
  }
]

const currentBase = ref('tian')
const showDropdown = ref(false)
const mapLayers = ref({})

// 初始化图层引用
const initLayers = () => {
  if (!props.map) return

  const layers = props.map.getLayers().getArray()
  console.log("地图中的图层:", layers)
  mapLayers.value = {}

  layers.forEach(layer => {
    const name = layer.get('name')
    if (name && ['tian', 'tianlabel', 'baidu', 'gaode'].includes(name)) {
      mapLayers.value[name] = layer
      console.log(`找到图层: ${name}`)

      if (layer.getVisible() && name !== 'tianlabel') {
        currentBase.value = name
      }
    }
  })

  console.log("初始化的图层:", mapLayers.value)
  console.log("当前选中的底图:", currentBase.value)
}

// 设置图层可见性
const setBaseVisible = (type) => {
  Object.entries(mapLayers.value).forEach(([key, layer]) => {
    if (key === 'tianlabel') {
      layer.setVisible(type === 'tian')
    } else {
      layer.setVisible(key === type)
    }
  })
}

// 切换底图
const switchBase = (type) => {
  currentBase.value = type
  setBaseVisible(type)
  showDropdown.value = false

  // 触发自定义事件，通知底图已切换
  if (props.map) {
    props.map.getLayers().forEach(layer => {
      if (layer.get('name') === type) {
        layer.dispatchEvent('propertychange')
      }
    })
  }
}

// 切换下拉菜单
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

// 点击外部关闭下拉菜单
const handleClickOutside = (event) => {
  const mapSwitcher = document.querySelector('.map-switcher')
  if (mapSwitcher && !mapSwitcher.contains(event.target)) {
    showDropdown.value = false
  }
}

// 监听地图实例变化
watch(() => props.map, (map) => {
  if (map) {
    initLayers()
  }
}, { immediate: true })

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<template>
  <div class="map-switcher">
    <div class="map-switcher-toggle" @click.stop="toggleDropdown">
      <span class="toggle-icon">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
          <path d="M11.99 18.54l-7.37-5.73L3 14.07l9 7 9-7-1.63-1.27zM12 16l7.36-5.73L21 9l-9-7-9 7 1.63 1.27L12 16"></path>
        </svg>
      </span>
      <span>图层管理</span>
      <div class="triangle" :class="{ 'triangle-up': showDropdown }"></div>
    </div>
    <div class="map-switcher-dropdown" v-if="showDropdown">
      <!-- 底图切换部分 -->
      <div
        v-for="item in baseTypes"
        :key="item.key"
        class="map-switcher-item"
        :class="{ active: currentBase === item.key }"
        @click="switchBase(item.key)"
      >
        <span class="map-switcher-icon" v-html="item.icon"></span>
        <span class="map-switcher-label">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<style>
.map-switcher {
  position: absolute;
  top: 10px;
  left: 50px;
  z-index: 4000;
  font-size: 15px;
  color: #333;
  min-width: 120px;
}

.map-switcher-toggle {
  display: flex;
  align-items: center;
  background-color: #fff;
  border: none;
  border-radius: 10px;
  padding: 8px 18px;
  cursor: pointer;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  user-select: none;
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
  transition: background 0.2s;
}

.toggle-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
}

.map-switcher-toggle:hover {
  background-color: #f5f7fa;
}

.triangle {
  width: 0;
  height: 0;
  margin-left: 8px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #1890ff;
  transition: transform 0.2s ease;
}

.triangle-up {
  transform: rotate(180deg);
}

.map-switcher-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 220px;
  background-color: #fff;
  border: none;
  border-radius: 10px;
  margin-top: 8px;
  box-shadow: 0 4px 16px rgba(0,0,0,0.10);
  overflow: hidden;
  z-index: 5000;
  padding: 6px 0;
}



.map-switcher-item {
  padding: 10px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  color: #333;
  display: flex;
  align-items: center;
}

.map-switcher-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: #666;
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.map-switcher-label {
  font-size: 14px;
}

.map-switcher-item:hover {
  background-color: #f5f7fa;
}

.map-switcher-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
  font-weight: 500;
}


</style>
