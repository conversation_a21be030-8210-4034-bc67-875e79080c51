<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import Draw from 'ol/interaction/Draw.js'
import { Vector as VectorSource } from 'ol/source.js'
import { Vector as VectorLayer } from 'ol/layer.js'
import { Style, Circle as CircleStyle, Fill, Stroke, Text } from 'ol/style.js'
import Overlay from 'ol/Overlay.js'
import { Polygon } from 'ol/geom.js'
import Feature from 'ol/Feature.js'
import { getArea } from 'ol/sphere.js'
import { unByKey } from 'ol/Observable.js'

const props = defineProps({
  map: Object
})

const drawing = ref(false)
const source = ref(null)
const vector = ref(null)
const draw = ref(null)
const polygonCounter = ref(0)
const tooltipElement = ref(null)
const tooltip = ref(null)
const listener = ref(null)
const measureTooltipElement = ref(null)
const measureTooltip = ref(null)
const measureTooltips = ref([])
const measureTooltipElements = ref([])

const initDraw = () => {
  if (!props.map) return

  // 创建矢量源和图层
  source.value = new VectorSource()
  vector.value = new VectorLayer({
    source: source.value,
    style: (feature) => {
      const polygonName = feature.get('name') || ''

      return new Style({
        fill: new Fill({
          color: 'rgba(24, 144, 255, 0.2)'
        }),
        stroke: new Stroke({
          color: '#1890ff',
          width: 2
        }),
        text: new Text({
          text: polygonName,
          font: '14px sans-serif',
          fill: new Fill({
            color: '#333'
          }),
          stroke: new Stroke({
            color: 'white',
            width: 3
          })
        })
      })
    },
    zIndex: 1000
  })

  props.map.addLayer(vector.value)

  createTooltip()
}

// 创建提示框
const createTooltip = () => {
  if (tooltipElement.value) {
    tooltipElement.value.parentNode.removeChild(tooltipElement.value)
  }

  tooltipElement.value = document.createElement('div')
  tooltipElement.value.className = 'ol-tooltip'
  tooltip.value = new Overlay({
    element: tooltipElement.value,
    offset: [15, 0],
    positioning: 'center-left'
  })

  props.map.addOverlay(tooltip.value)
}

// 创建测量提示框
const createMeasureTooltip = () => {
  const element = document.createElement('div')
  element.className = 'ol-tooltip ol-tooltip-measure'

  const tooltip = new Overlay({
    element: element,
    offset: [0, -15],
    positioning: 'bottom-center',
    stopEvent: false,
    insertFirst: false
  })

  props.map.addOverlay(tooltip)

  measureTooltipElements.value.push(element)
  measureTooltips.value.push(tooltip)

  measureTooltipElement.value = element
  measureTooltip.value = tooltip
}

// 格式化面积
const formatArea = (polygon) => {
  const area = getArea(polygon)
  let output

  if (area > 1000000) {
    output = (Math.round(area / 1000000 * 1000) / 1000) + ' km²'
  } else if (area > 10000) {
    output = (Math.round(area / 10000 * 100) / 100) + ' 公顷'
  } else {
    output = (Math.round(area * 100) / 100) + ' m²'
  }

  return output
}

// 开始绘制多边形
const startDrawPolygon = () => {
  if (!props.map) return

  if (drawing.value) {
    stopDrawing()
  }

  drawing.value = true

  createMeasureTooltip()

  // 创建绘图交互
  draw.value = new Draw({
    source: source.value,
    type: 'Polygon',
    style: new Style({
      fill: new Fill({
        color: 'rgba(24, 144, 255, 0.2)'
      }),
      stroke: new Stroke({
        color: '#1890ff',
        lineDash: [6, 6],
        width: 2
      }),
      image: new CircleStyle({
        radius: 5,
        stroke: new Stroke({
          color: '#1890ff',
          width: 1.5
        }),
        fill: new Fill({
          color: 'rgba(255, 255, 255, 0.8)'
        })
      })
    })
  })

  props.map.addInteraction(draw.value)

  // 添加绘图开始事件
  draw.value.on('drawstart', (evt) => {
    createMeasureTooltip()

    const sketch = evt.feature

    let tooltipCoord = null

    // 添加几何变化监听
    listener.value = sketch.getGeometry().on('change', (e) => {
      const geom = e.target
      const output = formatArea(geom)
      tooltipCoord = geom.getInteriorPoint().getCoordinates()

      // 显示多边形的顶点坐标
      const coordinates = geom.getCoordinates()[0]
      if (coordinates && coordinates.length > 2) {
        // 清除之前的顶点标记
        measureTooltips.value.forEach(tooltip => {
          if (tooltip.getElement().className.includes('ol-tooltip-vertex')) {
            props.map.removeOverlay(tooltip)
            const index = measureTooltips.value.indexOf(tooltip)
            if (index > -1) {
              measureTooltips.value.splice(index, 1)
              measureTooltipElements.value.splice(index, 1)
            }
          }
        })

        for (let i = 0; i < coordinates.length - 1; i++) {
          // 创建顶点坐标提示框
          const vertexElement = document.createElement('div')
          vertexElement.className = 'ol-tooltip ol-tooltip-vertex'
          vertexElement.innerHTML = `点 ${i+1}`

          const vertexTooltip = new Overlay({
            element: vertexElement,
            offset: [0, -15],
            positioning: 'bottom-center',
            stopEvent: false,
            insertFirst: false
          })

          vertexTooltip.setPosition(coordinates[i])
          props.map.addOverlay(vertexTooltip)

          measureTooltipElements.value.push(vertexElement)
          measureTooltips.value.push(vertexTooltip)
        }
      }

      measureTooltipElement.value.innerHTML = `面积: ${output}`
      measureTooltip.value.setPosition(tooltipCoord)
    })
  })

  // 添加绘图结束事件
  draw.value.on('drawend', (evt) => {
    const feature = evt.feature
    const geometry = feature.getGeometry()

    polygonCounter.value++

    feature.set('name', `多边形 ${polygonCounter.value}`)

    const area = getArea(geometry)
    const formattedArea = formatArea(geometry)

    measureTooltipElement.value.className = 'ol-tooltip ol-tooltip-static'
    measureTooltip.value.setOffset([0, -7])

    const resultElement = document.createElement('div')
    resultElement.className = 'ol-tooltip ol-tooltip-result'
    resultElement.innerHTML = `总面积: ${formattedArea}`

    const resultTooltip = new Overlay({
      element: resultElement,
      offset: [0, 0],
      positioning: 'center-center',
      stopEvent: false
    })

    // 设置位置为多边形中心
    const center = geometry.getInteriorPoint().getCoordinates()
    resultTooltip.setPosition(center)
    props.map.addOverlay(resultTooltip)

    measureTooltipElements.value.push(resultElement)
    measureTooltips.value.push(resultTooltip)

    measureTooltipElement.value = null

    unByKey(listener.value)
    listener.value = null

    console.log(`多边形 ${polygonCounter.value}: ${formattedArea} (${area} 平方米)`)

    stopDrawing()
  })

  // 添加鼠标移动事件
  const pointerMoveHandler = (evt) => {
    if (evt.dragging || !drawing.value) {
      return
    }

    let helpMsg = '点击地图开始绘制多边形'

    if (draw.value && draw.value.sketchFeature_) {
      const sketchGeom = draw.value.sketchFeature_.getGeometry()
      const coords = sketchGeom.getCoordinates()[0]

      if (coords.length > 2) {
        helpMsg = `已添加 ${coords.length - 1} 个点，双击结束绘制`
      } else {
        helpMsg = '点击添加下一个点'
      }
    }

    if (tooltipElement.value) {
      tooltipElement.value.innerHTML = helpMsg
      tooltip.value.setPosition(evt.coordinate)
    }
  }

  props.map.on('pointermove', pointerMoveHandler)
}

// 停止绘制
const stopDrawing = () => {
  if (!props.map) return

  drawing.value = false

  // 移除绘图交互
  if (draw.value) {
    props.map.removeInteraction(draw.value)
    draw.value = null
  }

  // 移除提示框
  if (tooltip.value) {
    props.map.removeOverlay(tooltip.value)
    tooltip.value = null
  }

  if (tooltipElement.value) {
    if (tooltipElement.value.parentNode) {
      tooltipElement.value.parentNode.removeChild(tooltipElement.value)
    }
    tooltipElement.value = null
  }

  // 移除监听器
  if (listener.value) {
    unByKey(listener.value)
    listener.value = null
  }

  // 移除临时测量提示框
  measureTooltips.value.forEach(tooltip => {
    if (tooltip.getElement().className.includes('ol-tooltip-measure') &&
        !tooltip.getElement().className.includes('ol-tooltip-static')) {
      props.map.removeOverlay(tooltip)
      const index = measureTooltips.value.indexOf(tooltip)
      if (index > -1) {
        measureTooltips.value.splice(index, 1)
        measureTooltipElements.value.splice(index, 1)
      }
    }
  })

  createTooltip()
}

// 清除所有多边形
const clearPolygons = () => {
  if (!props.map) return

  stopDrawing()

  if (source.value) {
    source.value.clear()
  }

  // 移除所有覆盖物
  const overlays = props.map.getOverlays().getArray().slice()
  overlays.forEach(overlay => {
    const element = overlay.getElement()
    if (element && element.className &&
        (element.className.includes('ol-tooltip-static') ||
         element.className.includes('ol-tooltip-vertex') ||
         element.className.includes('ol-tooltip-result'))) {
      props.map.removeOverlay(overlay)
    }
  })

  // 清空测量提示框列表
  measureTooltips.value.forEach(tooltip => {
    props.map.removeOverlay(tooltip)
  })

  measureTooltipElements.value.forEach(element => {
    if (element.parentNode) {
      element.parentNode.removeChild(element)
    }
  })

  measureTooltips.value = []
  measureTooltipElements.value = []
  measureTooltip.value = null
  measureTooltipElement.value = null

  polygonCounter.value = 0

  createTooltip()

  // 显示清除成功的提示
  const successMsg = document.createElement('div')
  successMsg.className = 'ol-tooltip ol-tooltip-success'
  successMsg.innerHTML = '已清除所有多边形'

  const successTooltip = new Overlay({
    element: successMsg,
    offset: [0, 0],
    positioning: 'center-center',
    stopEvent: false
  })

  // 获取地图中心点
  const center = props.map.getView().getCenter()
  successTooltip.setPosition(center)
  props.map.addOverlay(successTooltip)

  // 2秒后自动移除提示
  setTimeout(() => {
    props.map.removeOverlay(successTooltip)
    if (successMsg.parentNode) {
      successMsg.parentNode.removeChild(successMsg)
    }
  }, 1500)
}

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    initDraw()
  }
}, { immediate: true })

// 组件卸载时清理
onUnmounted(() => {
  stopDrawing()

  if (props.map && vector.value) {
    props.map.removeLayer(vector.value)
  }
})

defineExpose({
  startDrawPolygon,
  stopDrawing,
  clearPolygons
})
</script>

<template>
</template>

<style>
.draw-tools {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.draw-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.draw-button {
  display: flex;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.button-icon {
  margin-right: 8px;
  font-size: 16px;
}

.draw-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.draw-button.active {
  background-color: #1890ff;
  color: white;
}

.draw-button.clear-button {
  background-color: #f5f5f5;
  color: #333;
}

.draw-button.clear-button:hover {
  background-color: #ff4d4f;
  color: white;
}

.draw-help {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  max-width: 200px;
}

.help-text p {
  margin: 0;
  line-height: 1.4;
}

/* 提示框样式 */
.ol-tooltip {
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  color: #333;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.ol-tooltip-measure {
  opacity: 1;
  font-weight: bold;
  background-color: rgba(24, 144, 255, 0.8);
  color: white;
}

.ol-tooltip-static {
  background-color: rgba(24, 144, 255, 0.8);
  color: white;
  border: 1px solid white;
}

.ol-tooltip-vertex {
  background-color: rgba(255, 255, 255, 0.8);
  color: #333;
  border: 1px solid #ff9500;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 8px;
}

.ol-tooltip-result {
  background-color: rgba(88, 86, 214, 0.9);
  color: white;
  border: 2px solid white;
  font-size: 14px;
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  pointer-events: none;
}

.ol-tooltip-success {
  background-color: rgba(24, 170, 255, 0.81);
  color: white;
  border: 2px solid white;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: fadeInOut 1.5s ease-in-out;
  pointer-events: none;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: scale(0.8); }
  20% { opacity: 1; transform: scale(1.1); }
  30% { opacity: 1; transform: scale(1); }
  80% { opacity: 1; }
  100% { opacity: 0; }
}
</style>
