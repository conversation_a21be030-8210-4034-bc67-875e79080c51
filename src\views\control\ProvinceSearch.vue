<template>
  <div class="province-search-container" :class="{
    'province-search-page': isStandalone,
    'province-search-dropdown': !isStandalone && !isTopLeft,
    'province-search-topleft': isTopLeft
  }">

    <!-- 搜索面板 -->
    <div class="search-panel">
      <div class="form-title">{{ isTopLeft ? '省份省会搜索' : '省份省会搜索' }}</div>
      <hr>

      <!-- 搜索类型选择 -->
      <div class="search-type-selector">
        <el-radio-group v-model="searchType">
          <el-radio-button label="province">省份</el-radio-button>
          <el-radio-button label="capital">省会</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 省份/省会选择 -->
      <div class="select-container">
        <el-select
          v-model="selectedLocation"
          :placeholder="searchType === 'province' ? '请选择省份' : '请选择省会'"
          filterable
          class="location-select"
          @change="handleLocationChange"
          popper-class="province-select-dropdown province-select-dropdown-container"
          :teleported="true"
          placement="bottom-start"
          append-to-body
          :popper-options="{
            modifiers: [
              {
                name: 'offset',
                options: {
                  offset: [0, 10],
                },
              },
              {
                name: 'preventOverflow',
                options: {
                  padding: 20,
                  boundary: 'viewport',
                },
              },
              {
                name: 'computeStyles',
                options: {
                  gpuAcceleration: false, 
                },
              },
            ],
            strategy: 'fixed',
          }"
          :fit-input-width="false"
        >
          <el-option
            v-for="item in locationOptions"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </div>

      <!-- 搜索按钮 -->
      <div class="action-buttons">
        <el-button type="primary" size="small" @click="searchLocation">
          <span style="margin-right: 4px;">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="12" height="12" fill="currentColor">
              <path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8.94 3A8.994 8.994 0 0 0 13 3.06V1h-2v2.06A8.994 8.994 0 0 0 3.06 11H1v2h2.06A8.994 8.994 0 0 0 11 20.94V23h2v-2.06A8.994 8.994 0 0 0 20.94 13H23v-2h-2.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"></path>
            </svg>
          </span>
          定位
        </el-button>
        <el-button size="small" @click="resetSearch">重置</el-button>
      </div>

      <!-- 位置信息展示 -->
      <div v-if="currentLocation" class="location-info">
        <div class="info-title">{{ currentLocation.province }}</div>
        <div class="info-item">
          <span class="info-label">省会:</span>
          <span class="info-value">{{ currentLocation.capital }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">经度:</span>
          <span class="info-value">{{ currentLocation.longitude }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">纬度:</span>
          <span class="info-value">{{ currentLocation.latitude }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">简介:</span>
          <span class="info-value">{{ currentLocation.description }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, defineProps, defineEmits } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { transform } from 'ol/proj'
import Overlay from 'ol/Overlay'
import { getAllProvinces, findProvinceByName, getProvinceNames, getCapitalNames } from '@/data/ChinaProvinces'

// 定义组件属性
const props = defineProps({
  map: Object,
  isStandalone: {
    type: Boolean,
    default: false
  },
  isTopLeft: {
    type: Boolean,
    default: false
  }
})

// 定义事件
const emit = defineEmits(['location-selected'])
const router = useRouter()
const searchType = ref('province')
const selectedLocation = ref('')
const currentLocation = ref(null)

// 根据搜索找位置
const locationOptions = computed(() => {
  if (searchType.value === 'province') {
    return getProvinceNames()
  } else {
    return getCapitalNames()
  }
})

const handleLocationChange = (value) => {
  if (!value) return

  let location
  if (searchType.value === 'province') {
    location = findProvinceByName(value)
  } else {
    location = getAllProvinces().find(item => item.capital === value)
  }

  if (location) {
    currentLocation.value = location
  } else {
    ElMessage.warning('未找到相关位置信息')
    currentLocation.value = null
  }
}

// 搜索位置
const searchLocation = () => {
  if (!selectedLocation.value) {
    ElMessage.warning(searchType.value === 'province' ? '请选择省份' : '请选择省会')
    return
  }

  if (!currentLocation.value) {
    handleLocationChange(selectedLocation.value)
  }

  if (currentLocation.value) {
    // 定位
    locateToPosition(currentLocation.value.longitude, currentLocation.value.latitude)
    emit('location-selected', currentLocation.value)
  }
}

// 重置
const resetSearch = () => {
  selectedLocation.value = ''
  currentLocation.value = null
}


const locateToPosition = (longitude, latitude) => {
  if (!props.map) {
    ElMessage.warning('地图未初始化')
    return
  }

  try {
    const position = transform([longitude, latitude], 'EPSG:4326', 'EPSG:3857')
    const view = props.map.getView()

    view.animate({
      center: position,
      zoom: 10,  
      duration: 1000  
    })

    addMarker(position, currentLocation.value)

    ElMessage.success(`已定位到${currentLocation.value.province} - ${currentLocation.value.capital}`)
  } catch (error) {
    console.error('定位失败:', error)
    ElMessage.error('定位失败，请重试')
  }
}

const addMarker = (position, locationInfo) => {
  if (!props.map) return

  removeMarkers()

  // 创建标记点元素
  const markerElement = document.createElement('div')
  markerElement.className = 'province-marker'
  markerElement.innerHTML = `
    <div class="marker-pin"></div>
    <div class="marker-label">${locationInfo.capital}</div>
  `

  // 创建覆盖物
  const marker = new Overlay({
    element: markerElement,
    position: position,
    positioning: 'bottom-center',
    offset: [0, -10],
    stopEvent: false,
    id: 'province-marker'
  })

  // 添加到地图
  props.map.addOverlay(marker)

  // 添加点击事件
  markerElement.addEventListener('click', () => {
    showLocationInfo(locationInfo)
  })
}

// 移除标记点
const removeMarkers = () => {
  if (!props.map) return

  // 获取所有覆盖物
  const overlays = props.map.getOverlays().getArray()

  // 查找并移除标记点覆盖物
  overlays.forEach(overlay => {
    if (overlay.get('id') === 'province-marker') {
      props.map.removeOverlay(overlay)
    }
  })
}

// 显示位置信息
const showLocationInfo = (locationInfo) => {
  ElMessage({
    message: `${locationInfo.province} - ${locationInfo.capital}`,
    type: 'info',
    duration: 3000
  })
}


// 组件挂载时初始化
onMounted(() => {
  if (props.isStandalone) {
  }
})
</script>

<style>
.province-search-container {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  width: 260px; 
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.province-search-page {
  position: relative;
  top: auto;
  left: auto;
  width: 100%;
  height: 100%;
  border-radius: 0;
  box-shadow: none;
}

.province-search-dropdown {
  position: relative;
  top: auto;
  left: auto;
  width: 100%;
  border-radius: 10px;
  box-shadow: none;
  max-height: 80vh; 
  overflow-y: auto;
  padding-bottom: 10px; 
}

.province-search-topleft {
  position: absolute;
  top: 32px;
  left: 90px; 
  z-index: 1000;
  width: 260px; 
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  overflow: visible; 
  border: 1px solid #eaeaea;
}

.province-search-topleft .form-title {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
}

.province-search-topleft .location-select {
  width: 100%;
  font-size: 14px;
}

.province-search-topleft .action-buttons {
  margin-top: 10px;
}

.search-panel {
  padding: 8px;
}

.form-title {
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 8px;
  color: #409EFF;
  letter-spacing: 0.5px;
}

hr {
  border: none;
  border-top: 1px solid #eee;
  margin: 8px 0;
}

.search-type-selector {
  margin-bottom: 12px;
  display: flex;
  justify-content: center;
}

.select-container {
  margin-bottom: 12px;
}

.location-select {
  width: 100%;
  font-size: 13px;
}

.el-select-dropdown__wrap {
  max-width: none !important;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  margin-top: 14px;
}

.location-info {
  background-color: #f5f9ff;
  padding: 12px;
  border-radius: 6px;
  margin-top: 14px;
  border: 1px solid #e6f7ff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
}

.info-title {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 10px;
  color: #409EFF;
  text-align: center;
}

.info-item {
  margin-bottom: 6px;
  font-size: 13px;
  display: flex;
  line-height: 1.4;
}

.info-label {
  color: #606266;
  width: 45px;
  flex-shrink: 0;
  font-weight: 600;
}

.info-value {
  color: #303133;
  flex-grow: 1;
}

.back-btn {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1100;
  background-color: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  color: #606266;
  transition: all 0.3s;
}

.back-btn:hover {
  background-color: #f5f7fa;
  color: #409EFF;
}

.province-marker {
  position: relative;
  cursor: pointer;
}

.marker-pin {
  width: 20px;
  height: 20px;
  border-radius: 50% 50% 50% 0;
  background: #409EFF;
  position: absolute;
  transform: rotate(-45deg);
  left: 50%;
  top: 50%;
  margin: -15px 0 0 -10px;
}

.marker-pin:after {
  content: '';
  width: 14px;
  height: 14px;
  margin: 3px 0 0 3px;
  background: white;
  position: absolute;
  border-radius: 50%;
}

.marker-label {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
  color: #333;
  background-color: white;
  padding: 3px 8px;
  border-radius: 10px;
  font-size: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

.province-select-dropdown {
  z-index: 10100 !important;
  max-height: 300px !important; 
  overflow-y: auto !important; 
  min-width: 160px !important; 
  width: auto !important; 
  padding-right: 5px !important; 
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;

  scrollbar-width: thin !important; 
  scrollbar-color: rgba(0,0,0,0.2) transparent !important; 
}

.province-select-dropdown::-webkit-scrollbar {
  width: 6px !important;
}

.province-select-dropdown::-webkit-scrollbar-track {
  background: transparent !important;
}

.province-select-dropdown::-webkit-scrollbar-thumb {
  background-color: rgba(0,0,0,0.2) !important;
  border-radius: 6px !important;
  border: 2px solid transparent !important;
}

.province-select-dropdown .el-select-dropdown__item {
  padding: 6px 10px !important;
  font-size: 13px !important;
  height: auto !important;
  line-height: 1.4 !important;
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
  min-width: 160px !important; 
  background-color: transparent !important;
}

.province-select-dropdown .el-scrollbar__view {
  padding-right: 5px !important;
}

.province-select-dropdown .el-select-dropdown__wrap,
.province-select-dropdown .el-scrollbar,
.province-select-dropdown .el-select-dropdown__list {
  background-color: transparent !important;
  box-shadow: none !important;
}

.el-popper.is-light {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

.province-select-dropdown-container {
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
  border-radius: 6px !important;
  padding: 5px 0 !important;
  margin-top: 5px !important;
}

.province-select-dropdown-container .el-popper__arrow {
  display: none !important;
}

.province-select-dropdown .el-select-dropdown__item.hover,
.province-select-dropdown .el-select-dropdown__item:hover {
  background-color: #f0f9ff !important;
}
</style>
