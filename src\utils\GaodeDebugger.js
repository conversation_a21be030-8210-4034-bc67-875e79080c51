

/**
 * 检查高德地图API密钥配置
 * @returns {Object} 检查结果
 */
export function checkGaodeApiKey() {
  const result = {
    isConfigured: false,
    apiKey: null,
    version: null,
    errors: []
  };

  // 检查全局配置对象是否存在
  if (!window.gaodeConfig) {
    result.errors.push('未找到全局配置对象 window.gaodeConfig');
    return result;
  }

  // 检查API密钥是否配置
  if (!window.gaodeConfig.apiKey) {
    result.errors.push('未配置高德地图API密钥 (apiKey)');
  } else {
    result.isConfigured = true;
    result.apiKey = window.gaodeConfig.apiKey;
  }

  // 检查版本配置
  result.version = window.gaodeConfig.version || '2.0';

  return result;
}

/**
 * 测试高德地图API连接
 * @returns {Promise<Object>} 测试结果
 */
export async function testGaodeApiConnection() {
  const result = {
    success: false,
    message: '',
    details: null
  };

  try {
    // 检查API密钥配置
    const keyCheck = checkGaodeApiKey();
    if (!keyCheck.isConfigured) {
      result.message = '高德地图API密钥未正确配置';
      result.details = keyCheck;
      return result;
    }

    // 测试一个简单的API调用 - 地理编码
    const response = await fetch(`https://restapi.amap.com/v3/geocode/geo?address=北京&output=JSON&key=${keyCheck.apiKey}`);
    const data = await response.json();

    if (data.status === '1') {
      result.success = true;
      result.message = 'API连接成功';
      result.details = data;
    } else {
      result.message = `API连接失败: ${data.info || '未知错误'}`;
      result.details = data;
    }
  } catch (error) {
    result.message = `API连接测试出错: ${error.message}`;
    result.details = error;
  }

  return result;
}

/**
 * 检查路线绘制问题
 * @param {Object} map - OpenLayers地图实例
 * @param {Object} routeSource - 路线矢量源
 * @returns {Object} 检查结果
 */
export function checkRouteRendering(map, routeSource) {
  const result = {
    success: false,
    message: '',
    details: {
      mapExists: false,
      sourceExists: false,
      featuresCount: 0,
      projection: null,
      extent: null
    }
  };

  // 检查地图实例
  if (!map) {
    result.message = '地图实例不存在';
    return result;
  }
  result.details.mapExists = true;

  // 检查地图投影
  const view = map.getView();
  if (view) {
    result.details.projection = view.getProjection().getCode();
  }

  // 检查路线源
  if (!routeSource) {
    result.message = '路线矢量源不存在';
    return result;
  }
  result.details.sourceExists = true;

  // 检查特征数量
  const features = routeSource.getFeatures();
  result.details.featuresCount = features.length;

  if (features.length === 0) {
    result.message = '路线矢量源中没有特征';
    return result;
  }

  // 检查范围
  try {
    const extent = routeSource.getExtent();
    result.details.extent = extent;
    
    // 检查范围是否有效
    if (extent[0] === Infinity || extent[1] === Infinity || 
        extent[2] === -Infinity || extent[3] === -Infinity) {
      result.message = '路线范围无效';
      return result;
    }
    
    result.success = true;
    result.message = '路线绘制检查通过';
  } catch (error) {
    result.message = `获取路线范围时出错: ${error.message}`;
    result.details.error = error;
  }

  return result;
}

/**
 * 运行完整的高德地图API和路线绘制诊断
 * @param {Object} map - OpenLayers地图实例
 * @param {Object} routeSource - 路线矢量源
 * @returns {Promise<Object>} 诊断结果
 */
export async function runGaodeDiagnostics(map, routeSource) {
  const result = {
    apiKeyCheck: checkGaodeApiKey(),
    apiConnectionTest: null,
    routeRenderingCheck: null,
    timestamp: new Date().toISOString(),
    overallStatus: 'failed',
    recommendations: []
  };

  // 测试API连接
  result.apiConnectionTest = await testGaodeApiConnection();

  // 检查路线绘制
  if (map && routeSource) {
    result.routeRenderingCheck = checkRouteRendering(map, routeSource);
  }

  // 生成建议
  if (!result.apiKeyCheck.isConfigured) {
    result.recommendations.push('请在public/config/gaode.js文件中配置有效的高德地图API密钥');
  }

  if (result.apiConnectionTest && !result.apiConnectionTest.success) {
    result.recommendations.push('API连接失败，请检查网络连接和API密钥是否有效');
    
    if (result.apiConnectionTest.details && result.apiConnectionTest.details.info === 'INVALID_USER_KEY') {
      result.recommendations.push('API密钥无效，请确保使用正确的密钥并检查是否有相应的权限');
    }
  }

  if (result.routeRenderingCheck) {
    if (!result.routeRenderingCheck.success) {
      if (result.routeRenderingCheck.details.featuresCount === 0) {
        result.recommendations.push('路线数据未能正确加载或解析，请检查API返回的路线数据');
      }
      
      if (result.routeRenderingCheck.details.extent && 
          (result.routeRenderingCheck.details.extent[0] === Infinity || 
           result.routeRenderingCheck.details.extent[1] === Infinity)) {
        result.recommendations.push('路线范围无效，可能是坐标转换问题');
      }
    }
  }

  // 设置整体状态
  if (result.apiKeyCheck.isConfigured && 
      result.apiConnectionTest && result.apiConnectionTest.success && 
      result.routeRenderingCheck && result.routeRenderingCheck.success) {
    result.overallStatus = 'success';
  } else if (result.apiKeyCheck.isConfigured && 
             result.apiConnectionTest && result.apiConnectionTest.success) {
    result.overallStatus = 'partial';
  }

  return result;
}
