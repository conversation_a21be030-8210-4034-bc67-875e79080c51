<template>
  <div class="topleft-province-btn" @click="toggleProvinceSearch" ref="provinceBtn">
    <span class="btn-icon">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
      </svg>
    </span>
    <span class="btn-text">省份搜索</span>
    <span class="btn-arrow">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="currentColor">
        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
      </svg>
    </span>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const provinceBtn = ref(null)
const emit = defineEmits(['toggle-province-search'])

function toggleProvinceSearch() {
  // 获取按钮位置
  if (provinceBtn.value) {
    const rect = provinceBtn.value.getBoundingClientRect()
    emit('toggle-province-search', { rect })
  } else {
    emit('toggle-province-search', {})
  }
}
</script>

<style scoped>
.topleft-province-btn {
  position: absolute;
  top: 32px;
  left: 90px; 
  z-index: 1010;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 4px;
  padding: 10px 16px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  cursor: pointer;
  transition: all 0.3s;
  font-size: 14px;
  color: #409EFF;
  font-weight: 500;
  border: 1px solid #e6f7ff;
  width: 180px;
  justify-content: flex-start;
}

.topleft-province-btn::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 4px;
  background-color: #409EFF;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  transition: height 0.3s ease;
  height: 0;
}

.topleft-province-btn:hover {
  background-color: #f0f9ff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.topleft-province-btn:active {
  background-color: #e6f7ff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.1);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  color: #409EFF;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 50%;
  padding: 6px;
  width: 24px;
  height: 24px;
}

.btn-text {
  white-space: nowrap;
  letter-spacing: 0.5px;
  font-weight: 500;
  flex-grow: 1;
}

.btn-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409EFF;
  opacity: 0.7;
  transition: all 0.3s;
  margin-left: 5px;
}

.topleft-province-btn:hover .btn-arrow {
  opacity: 1;
  transform: translateY(2px);
}

/* 添加动画效果 */
.topleft-province-btn {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加动画 */
.btn-icon svg {
  transition: transform 0.3s ease;
}

.topleft-province-btn:hover .btn-icon svg {
  transform: scale(1.1);
}

.topleft-province-btn:hover::before {
  height: 100%;
}
</style>
