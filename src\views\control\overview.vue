<script setup>
import OverviewMap from 'ol/control/OverviewMap.js'
import TileLayer from 'ol/layer/Tile.js'
import { defineProps, watch, ref, onUnmounted } from 'vue'

const props = defineProps({
  map: Object
})

const overviewMapControl = ref(null)

const createOverviewMap = (map) => {
  if (!map) return

  console.log("创建鹰眼控件")

  try {
    const layers = map.getLayers().getArray()
    const baseLayer = layers.find(layer =>
      layer.getVisible() &&
      (layer.get('name') === 'tian' || layer.get('name') === 'baidu' || layer.get('name') === 'gaode')
    )

    if (!baseLayer) {
      console.error("未找到可见的底图图层")
      return
    }

    console.log("找到底图图层:", baseLayer.get('name'))

    const overviewLayer = new TileLayer({ source: baseLayer.getSource() })

    if (overviewMapControl.value) {
      console.log("更新已存在的鹰眼控件")

      const ovMap = overviewMapControl.value.getOverviewMap()

      const ovLayers = ovMap.getLayers()
      const layersToRemove = []
      ovLayers.forEach(layer => {
        layersToRemove.push(layer)
      })

      layersToRemove.forEach(layer => {
        ovMap.removeLayer(layer)
      })

      ovMap.addLayer(overviewLayer)

      console.log("鹰眼控件图层已更新为:", baseLayer.get('name'))
    } else {
      console.log("创建新的鹰眼控件")

      // 创建鹰眼控件
      const miniMap = new OverviewMap({
        className: 'ol-overviewmap',
        collapsed: false,
        collapsible: false,
        layers: [overviewLayer],
        tipLabel: ''
      })

      map.addControl(miniMap)
      console.log("鹰眼控件已添加到地图")

      overviewMapControl.value = miniMap

      setTimeout(() => {
        console.log("设置鹰眼控件样式")
        const overviewElement = document.querySelector('.ol-overviewmap')
        console.log("找到鹰眼元素:", overviewElement)

        if (overviewElement) {
          overviewElement.style.display = 'block'
          overviewElement.style.zIndex = '9000'
          overviewElement.style.left = '15px'
          overviewElement.style.bottom = '50px'
          overviewElement.style.right = 'auto'
          overviewElement.style.border = 'none'
          overviewElement.style.backgroundColor = 'transparent'

          const mapElement = overviewElement.querySelector('.ol-overviewmap-map')
          if (mapElement) {
            mapElement.style.display = 'block'
            mapElement.style.height = '160px'
            mapElement.style.width = '160px'
          }
        } else {
          console.error("未找到鹰眼控件元素，请检查DOM结构")
        }
      }, 1000)
    }
  } catch (error) {
    console.error("创建/更新鹰眼控件时出错:", error)
  }
}

// 移除鹰眼控件函数
const removeOverviewMap = () => {
  if (props.map && overviewMapControl.value) {
    props.map.removeControl(overviewMapControl.value)
    overviewMapControl.value = null
  }
}

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    createOverviewMap(map)

    // 添加图层可见性变化监听器
    const layers = map.getLayers().getArray()
    layers.forEach(layer => {
      if (layer.get('name') === 'tian' || layer.get('name') === 'baidu' || layer.get('name') === 'gaode') {
        layer.on('propertychange', (event) => {
          if (event.key === 'visible' && event.target.getVisible()) {
            console.log(`图层 ${layer.get('name')} 可见性变化为: ${layer.getVisible()}`)
            createOverviewMap(map)
          }
        })
      }
    })
  }
}, { immediate: true })

// 组件卸载时移除控件
onUnmounted(() => {
  removeOverviewMap()
})

defineExpose({
  createOverviewMap,
  removeOverviewMap,
  overviewMapControl
})
</script>

<template>
</template>

<style>
.ol-overviewmap {
  left: 20px;
  bottom: 60px;
  z-index: 9000;
  background: rgba(255,255,255,0.85);
  border-radius: 16px;
  box-shadow: 0 6px 24px rgba(0,0,0,0.10), 0 1.5px 4px rgba(0,0,0,0.08);
  border: none;
  padding: 0;
  transition: box-shadow 0.2s, transform 0.2s;
}
.ol-overviewmap:hover {
  box-shadow: 0 12px 32px rgba(0,0,0,0.16), 0 3px 8px rgba(0,0,0,0.10);
  transform: scale(1.03);
}
.ol-overviewmap .ol-overviewmap-map {
  border-radius: 14px;
  box-shadow: none;
  background: #f8fafb;
  border: 1.5px solid #e5e6eb;
}
.ol-overviewmap .ol-overviewmap-box {
  border: 2px solid #1890ff;
  background: rgba(24,144,255,0.08);
  border-radius: 3px;
}
.ol-overviewmap::before {
  position: absolute;
  top: -26px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #666;
  background: rgba(255,255,255,0.7);
  border-radius: 8px;
  padding: 2px 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
  letter-spacing: 2px;
}
</style>