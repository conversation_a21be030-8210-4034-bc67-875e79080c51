<script setup>
import MousePosition from 'ol/control/MousePosition.js'
import { ref, onMounted, onUnmounted, watch } from 'vue'
import Overlay from 'ol/Overlay.js'
import { transform } from 'ol/proj.js'
import { toStringHDMS } from 'ol/coordinate.js'

const props = defineProps({
  map: Object
})

const showCoordinatesEnabled = ref(false)
const coordinateElement = ref(null)
const coordinateOverlay = ref(null)

const removeDefaultMousePosition = () => {
  if (!props.map) return

  // 获取所有控件
  const controls = props.map.getControls().getArray()

  for (let i = 0; i < controls.length; i++) {
    if (controls[i] instanceof MousePosition) {
      props.map.removeControl(controls[i])
      console.log('已移除默认的鼠标位置控件')
      break
    }
  }
}

// 格式化坐标
const formatCoordinate = (coordinate) => {
  return toStringHDMS(transform(coordinate, 'EPSG:3857', 'EPSG:4326'))
}

// 创建坐标提示框
const createCoordinateTooltip = () => {
  if (coordinateElement.value) {
    coordinateElement.value.parentNode?.removeChild(coordinateElement.value)
  }

  coordinateElement.value = document.createElement('div')
  coordinateElement.value.className = 'ol-tooltip ol-tooltip-coordinate'

  coordinateElement.value.style.display = 'block'
  coordinateElement.value.style.position = 'absolute'

  // 创建Overlay
  coordinateOverlay.value = new Overlay({
    element: coordinateElement.value,
    offset: [0, 20], 
    positioning: 'top-center', 
    stopEvent: false,
    insertFirst: true 
  })

  // 添加到地图
  props.map.addOverlay(coordinateOverlay.value)

  console.log('创建了坐标提示框')
}

// 移除坐标提示框
const removeCoordinateTooltip = () => {
  if (coordinateOverlay.value) {
    props.map.removeOverlay(coordinateOverlay.value)
    coordinateOverlay.value = null
  }

  if (coordinateElement.value && coordinateElement.value.parentNode) {
    coordinateElement.value.parentNode.removeChild(coordinateElement.value)
    coordinateElement.value = null
  }
}

// 处理左键点击事件
const handleClick = (evt) => {
  if (!showCoordinatesEnabled.value) return

  const coordinate = evt.coordinate

  if (!coordinate) {
    console.error('无法获取坐标')
    return
  }

  removeCoordinateTooltip()

  createCoordinateTooltip()

  const hdms = formatCoordinate(coordinate)

  // 获取经纬度坐标
  const lonLat = transform(coordinate, 'EPSG:3857', 'EPSG:4326')
  const longitude = lonLat[0].toFixed(6)
  const latitude = lonLat[1].toFixed(6)

  coordinateElement.value.innerHTML = `
    <div class="coordinate-title">坐标位置</div>
    <div class="coordinate-value">${hdms}</div>
    <div class="coordinate-decimal">
      <div>经度: ${longitude}</div>
      <div>纬度: ${latitude}</div>
    </div>
  `

  coordinateOverlay.value.setPosition(coordinate)

  coordinateElement.value.style.display = 'block'
  coordinateElement.value.style.visibility = 'visible'
  coordinateElement.value.style.opacity = '1'

  console.log('左键点击坐标:', coordinate, '格式化后:', hdms)
  console.log('经纬度:', longitude, latitude)
}

const toggleCoordinatesDisplay = () => {
  showCoordinatesEnabled.value = !showCoordinatesEnabled.value

  if (!showCoordinatesEnabled.value) {
    removeCoordinateTooltip()
  }
}

// 处理右键点击事件
const handleContextMenu = (evt) => {
  evt.preventDefault();

  if (!showCoordinatesEnabled.value) return

  const coordinate = evt.coordinate

  if (!coordinate) {
    console.error('无法获取坐标')
    return
  }

  removeCoordinateTooltip()

  createCoordinateTooltip()

  const hdms = formatCoordinate(coordinate)

  const lonLat = transform(coordinate, 'EPSG:3857', 'EPSG:4326')
  const longitude = lonLat[0].toFixed(6)
  const latitude = lonLat[1].toFixed(6)

  coordinateElement.value.innerHTML = `
    <div class="coordinate-title">坐标位置</div>
    <div class="coordinate-value">${hdms}</div>
    <div class="coordinate-decimal">
      <div>经度: ${longitude}</div>
      <div>纬度: ${latitude}</div>
    </div>
  `

  coordinateOverlay.value.setPosition(coordinate)

  coordinateElement.value.style.display = 'block'
  coordinateElement.value.style.visibility = 'visible'
  coordinateElement.value.style.opacity = '1'

  console.log('右键点击坐标:', coordinate, '格式化后:', hdms)
  console.log('经纬度:', longitude, latitude)
}

// 初始化
const initMousePosition = () => {
  if (!props.map) return

  removeDefaultMousePosition()

  if (props.map) {

    props.map.un('click', handleClick)
    props.map.un('contextmenu', handleContextMenu)

    props.map.on('click', handleClick)

    console.log('已添加左键点击事件监听')
  } else {
    console.error('地图实例不可用')
  }

  removeCoordinateTooltip()
}

// 清理
const cleanup = () => {
  if (!props.map) return

  if (props.map) {
    props.map.un('click', handleClick)
    props.map.un('contextmenu', handleContextMenu)
  }

  removeCoordinateTooltip()
}

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    initMousePosition()
  }
}, { immediate: true })

onUnmounted(() => {
  cleanup()
})

defineExpose({
  toggleCoordinatesDisplay,
  showCoordinatesEnabled,
  handleClick,
  handleContextMenu,
  createCoordinateTooltip,
  removeCoordinateTooltip
})
</script>

<template>
  <div class="mouse-position-tools">
    <button
      class="coordinate-button"
      :class="{ active: showCoordinatesEnabled }"
      @click="toggleCoordinatesDisplay"
      :title="showCoordinatesEnabled ? '关闭定位功能' : '开启定位功能'"
    >
      <div class="button-icon">
        <i class="fas fa-location-crosshairs"></i>
      </div>
      <span>定位</span>
    </button>
  </div>
</template>

<style>
  .mouse-position-tools {
    position: absolute;
    top: 10px;
    right: 15px;
    z-index: 9000;
  }

  .coordinate-button {
    background-color: #fff;
    border: none;
    border-radius: 10px;
    padding: 8px 18px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
    transition: background 0.2s, color 0.2s;
    color: #1890ff;
    letter-spacing: 0.5px;
  }

  .coordinate-button:hover {
    background-color: #f5f7fa;
    color: #40a9ff;
  }

  .coordinate-button.active {
    background: linear-gradient(to right, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.2));
    border-color: rgba(24, 144, 255, 0.3);
    color: #1890ff;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2), 0 1px 3px rgba(24, 144, 255, 0.1);
  }

  .button-icon {
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: inherit;
    transition: transform 0.3s ease;
  }

  .coordinate-button:hover .button-icon {
    transform: translateY(-2px);
  }

  .coordinate-button.active .button-icon {
    text-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
  }

  .ol-tooltip {
    position: absolute !important; 
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--radius-md);
    color: #333;
    padding: 12px 18px;
    white-space: nowrap;
    font-size: 12px;
    box-shadow: var(--shadow-lg);
    pointer-events: none;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    backdrop-filter: blur(4px);
    animation: tooltip-fade 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform-origin: top center;
  }

  @keyframes tooltip-fade {
    from {
      opacity: 0;
      transform: translateY(10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .ol-tooltip-coordinate {
    min-width: 200px;
    text-align: center;
    z-index: 10000;
    margin-top: 20px; 
  }

  /* 添加箭头指向点击位置 */
  .ol-tooltip-coordinate::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 0 8px 8px 8px;
    border-style: solid;
    border-color: transparent transparent rgba(255, 255, 255, 0.95) transparent;
    filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
  }

  /* 添加图标到标题 */
  .coordinate-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--primary-color);
    font-size: 14px;
    letter-spacing: 0.5px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .coordinate-title::before {
    content: '\f3c5'; /* 位置图标 */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 8px;
    font-size: 14px;
  }

  .coordinate-value {
    margin-bottom: 8px;
    font-weight: 500;
    font-size: 13px;
    letter-spacing: 0.3px;
  }

  .coordinate-decimal {
    font-size: 12px;
    color: #555;
    margin-top: 8px;
    text-align: left;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    padding-top: 8px;
    letter-spacing: 0.2px;
  }
</style>