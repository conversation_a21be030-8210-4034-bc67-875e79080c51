<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import Draw from 'ol/interaction/Draw.js'
import Modify from 'ol/interaction/Modify.js'
import { Vector as VectorSource } from 'ol/source.js'
import { Vector as VectorLayer } from 'ol/layer.js'
import { Style, Circle as CircleStyle, Fill, Stroke, Text } from 'ol/style.js'
import Overlay from 'ol/Overlay.js'
import { LineString } from 'ol/geom.js'
import Feature from 'ol/Feature.js'
import { getLength } from 'ol/sphere.js'
import { unByKey } from 'ol/Observable.js'

const props = defineProps({
  map: Object
})

const drawing = ref(false)
const source = ref(null)
const vector = ref(null)
const draw = ref(null)
const modify = ref(null)
const curveCounter = ref(0)
const tooltipElement = ref(null)
const tooltip = ref(null)
const controlPoints = ref([])
const initDraw = () => {
  if (!props.map) return

  // 创建矢量源和图层
  source.value = new VectorSource()
  vector.value = new VectorLayer({
    source: source.value,
    style: (feature) => {
      const curveName = feature.get('name') || ''
      const isBezier = feature.get('bezier') === true

      if (isBezier) {
        return new Style({
          stroke: new Stroke({
            color: '#722ed1',
            width: 3
          }),
          text: new Text({
            text: curveName,
            placement: 'line',
            font: '14px sans-serif',
            fill: new Fill({
              color: '#333'
            }),
            stroke: new Stroke({
              color: 'white',
              width: 3
            })
          })
        })
      } else {
        // 控制点样式
        return new Style({
          image: new CircleStyle({
            radius: 5,
            fill: new Fill({
              color: 'rgba(255, 0, 0, 0.7)'
            }),
            stroke: new Stroke({
              color: 'white',
              width: 2
            })
          })
        })
      }
    },
    zIndex: 1000
  })

  props.map.addLayer(vector.value)

  createTooltip()
}

// 创建提示框
const createTooltip = () => {
  if (tooltipElement.value) {
    tooltipElement.value.parentNode.removeChild(tooltipElement.value)
  }

  tooltipElement.value = document.createElement('div')
  tooltipElement.value.className = 'ol-tooltip'
  tooltip.value = new Overlay({
    element: tooltipElement.value,
    offset: [15, 0],
    positioning: 'center-left'
  })

  props.map.addOverlay(tooltip.value)
}

// 计算贝塞尔曲线点
const calculateBezierPoints = (points, numPoints = 100) => {
  const bezierPoints = []

  // 二次贝塞尔曲线 (3个点)
  if (points.length === 3) {
    for (let t = 0; t <= 1; t += 1 / numPoints) {
      const x = Math.pow(1 - t, 2) * points[0][0] +
                2 * (1 - t) * t * points[1][0] +
                Math.pow(t, 2) * points[2][0]

      const y = Math.pow(1 - t, 2) * points[0][1] +
                2 * (1 - t) * t * points[1][1] +
                Math.pow(t, 2) * points[2][1]

      bezierPoints.push([x, y])
    }
  }
  // 三次贝塞尔曲线 (4个点)
  else if (points.length === 4) {
    for (let t = 0; t <= 1; t += 1 / numPoints) {
      const x = Math.pow(1 - t, 3) * points[0][0] +
                3 * Math.pow(1 - t, 2) * t * points[1][0] +
                3 * (1 - t) * Math.pow(t, 2) * points[2][0] +
                Math.pow(t, 3) * points[3][0]

      const y = Math.pow(1 - t, 3) * points[0][1] +
                3 * Math.pow(1 - t, 2) * t * points[1][1] +
                3 * (1 - t) * Math.pow(t, 2) * points[2][1] +
                Math.pow(t, 3) * points[3][1]

      bezierPoints.push([x, y])
    }
  }

  return bezierPoints
}

// 开始绘制贝塞尔曲线
const startDrawBezier = () => {
  if (!props.map) return

  if (drawing.value) {
    stopDrawing()
  }

  drawing.value = true
  controlPoints.value = []

  draw.value = new Draw({
    source: source.value,
    type: 'Point',
    style: new Style({
      image: new CircleStyle({
        radius: 5,
        fill: new Fill({
          color: 'rgba(255, 0, 0, 0.7)'
        }),
        stroke: new Stroke({
          color: 'white',
          width: 2
        })
      })
    })
  })

  props.map.addInteraction(draw.value)

  // 添加绘图结束事件
  draw.value.on('drawend', (evt) => {
    const feature = evt.feature
    const geometry = feature.getGeometry()
    const coord = geometry.getCoordinates()

    controlPoints.value.push(coord)

    if (controlPoints.value.length >= 3) {
      const bezierPoints = calculateBezierPoints(controlPoints.value)

      const bezierFeature = new Feature({
        geometry: new LineString(bezierPoints)
      })

      curveCounter.value++

      bezierFeature.set('name', `曲线 ${curveCounter.value}`)
      bezierFeature.set('bezier', true)

      source.value.addFeature(bezierFeature)

      console.log(`曲线 ${curveCounter.value} 已创建，控制点数量: ${controlPoints.value.length}`)

      controlPoints.value = []

      const features = source.value.getFeatures()
      features.forEach(f => {
        if (!f.get('bezier')) {
          source.value.removeFeature(f)
        }
      })

      stopDrawing()
    } else {
      // 显示提示
      const msg = `已添加 ${controlPoints.value.length} 个控制点，需要至少 3 个点`
      if (tooltipElement.value) {
        tooltipElement.value.innerHTML = msg
      }
    }
  })

  // 添加鼠标移动事件
  const pointerMoveHandler = (evt) => {
    if (evt.dragging || !drawing.value) {
      return
    }

    let helpMsg = '点击地图添加贝塞尔曲线控制点'

    if (controlPoints.value.length > 0) {
      helpMsg = `已添加 ${controlPoints.value.length} 个控制点，继续添加或点击"停止绘制"完成`
    }

    if (tooltipElement.value) {
      tooltipElement.value.innerHTML = helpMsg
      tooltip.value.setPosition(evt.coordinate)
    }
  }

  props.map.on('pointermove', pointerMoveHandler)
}

// 停止绘制
const stopDrawing = () => {
  if (!props.map) return

  drawing.value = false

  if (draw.value) {
    props.map.removeInteraction(draw.value)
    draw.value = null
  }

  // 移除修改交互
  if (modify.value) {
    props.map.removeInteraction(modify.value)
    modify.value = null
  }

  // 移除提示框
  if (tooltip.value) {
    props.map.removeOverlay(tooltip.value)
    tooltip.value = null
  }

  if (tooltipElement.value) {
    if (tooltipElement.value.parentNode) {
      tooltipElement.value.parentNode.removeChild(tooltipElement.value)
    }
    tooltipElement.value = null
  }

  // 清除控制点
  if (controlPoints.value.length > 0) {
    if (controlPoints.value.length >= 3) {
      const bezierPoints = calculateBezierPoints(controlPoints.value)

      const bezierFeature = new Feature({
        geometry: new LineString(bezierPoints)
      })

      curveCounter.value++

      bezierFeature.set('name', `曲线 ${curveCounter.value}`)
      bezierFeature.set('bezier', true)

      source.value.addFeature(bezierFeature)

      console.log(`曲线 ${curveCounter.value} 已创建，控制点数量: ${controlPoints.value.length}`)
    }

    // 清除控制点
    const features = source.value.getFeatures()
    features.forEach(f => {
      if (!f.get('bezier')) {
        source.value.removeFeature(f)
      }
    })

    controlPoints.value = []
  }

  createTooltip()
}

// 清除所有曲线
const clearCurves = () => {
  if (!props.map) return

  stopDrawing()

  if (source.value) {
    source.value.clear()
  }

  curveCounter.value = 0

  controlPoints.value = []

  createTooltip()

  // 显示清除成功的提示
  const successMsg = document.createElement('div')
  successMsg.className = 'ol-tooltip ol-tooltip-success'
  successMsg.innerHTML = '已清除所有曲线'

  const successTooltip = new Overlay({
    element: successMsg,
    offset: [0, 0],
    positioning: 'center-center',
    stopEvent: false
  })

  // 获取地图中心点
  const center = props.map.getView().getCenter()
  successTooltip.setPosition(center)
  props.map.addOverlay(successTooltip)

  // 2秒后自动移除提示
  setTimeout(() => {
    props.map.removeOverlay(successTooltip)
    if (successMsg.parentNode) {
      successMsg.parentNode.removeChild(successMsg)
    }
  }, 1500)
}

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    initDraw()
  }
}, { immediate: true })

// 组件卸载时清理
onUnmounted(() => {
  stopDrawing()

  if (props.map && vector.value) {
    props.map.removeLayer(vector.value)
  }
})

defineExpose({
  startDrawBezier,
  stopDrawing,
  clearCurves
})
</script>

<template>
</template>

<style>
.draw-tools {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.draw-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.draw-button {
  display: flex;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.button-icon {
  margin-right: 8px;
  font-size: 16px;
}

.draw-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.draw-button.active {
  background-color: #722ed1;
  color: white;
}

.draw-button.clear-button {
  background-color: #f5f5f5;
  color: #333;
}

.draw-button.clear-button:hover {
  background-color: #ff4d4f;
  color: white;
}

.draw-help {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  max-width: 200px;
}

.help-text p {
  margin: 0;
  line-height: 1.4;
}

/* 提示框样式 */
.ol-tooltip {
  position: relative;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4px;
  color: #333;
  padding: 4px 8px;
  white-space: nowrap;
  font-size: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
  pointer-events: none;
}

.ol-tooltip-success {
  background-color: rgba(24, 144, 255, 0.9);
  color: white;
  border: 2px solid white;
  font-size: 14px;
  font-weight: bold;
  padding: 8px 16px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: fadeInOut 1.5s ease-in-out;
  pointer-events: none;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: scale(0.8); }
  20% { opacity: 1; transform: scale(1.1); }
  30% { opacity: 1; transform: scale(1); }
  80% { opacity: 1; }
  100% { opacity: 0; }
}
</style>
