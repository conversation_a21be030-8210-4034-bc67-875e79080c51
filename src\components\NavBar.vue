<script setup>
import { ref } from 'vue'

// 定义组件属性
const props = defineProps({
  mapInstance: Object,
  positionEnabled: <PERSON>olean,
  layerManagerEnabled: <PERSON><PERSON>an,
  mapOperationsEnabled: <PERSON>olean,
  drawToolsEnabled: <PERSON><PERSON>an,
  measureToolsEnabled: <PERSON><PERSON><PERSON>
})

// 定义事件
const emit = defineEmits([
  'toggle-layer-manager',
  'toggle-map-operations',
  'toggle-draw-tools',
  'toggle-measure-tools',
  'toggle-position'
])

// 底图类型列表
const baseTypes = [
  { key: 'tian', label: '天地图' },
  { key: 'baidu', label: '百度地图' },
  { key: 'gaode', label: '高德地图' }
]

// 切换图层管理功能
const toggleLayerManager = () => {
  emit('toggle-layer-manager')
}

// 切换地图操作功能
const toggleMapOperations = () => {
  emit('toggle-map-operations')
}

// 切换绘制工具功能
const toggleDrawTools = () => {
  emit('toggle-draw-tools')
}

// 切换测量工具功能
const toggleMeasureTools = () => {
  emit('toggle-measure-tools')
}

// 切换定位功能
const togglePosition = () => {
  emit('toggle-position')
}
</script>

<template>
  <!-- 顶部导航栏 -->
  <div class="navbar">
    <div class="navbar-container">
      <div class="navbar-logo">
        <span>WebGIS系统</span>
      </div>

      <!-- 导航栏按钮 -->
      <div class="navbar-buttons">
        <!-- 图层管理 -->
        <button
          class="navbar-button"
          :class="{ active: layerManagerEnabled }"
          @click="toggleLayerManager"
        >
          <div class="navbar-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
              <path d="M11.99 18.54l-7.37-5.73L3 14.07l9 7 9-7-1.63-1.27zM12 16l7.36-5.73L21 9l-9-7-9 7 1.63 1.27L12 16"></path>
            </svg>
          </div>
          <span class="navbar-text">图层管理</span>
        </button>

        <!-- 地图操作 -->
        <button
          class="navbar-button"
          :class="{ active: mapOperationsEnabled }"
          @click="toggleMapOperations"
        >
          <div class="navbar-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
              <path d="M15.5 14h-.79l-.28-.27a6.5 6.5 0 0 0 1.48-5.34c-.47-2.78-2.79-5-5.59-5.34a6.505 6.505 0 0 0-7.27 7.27c.34 2.8 2.56 5.12 5.34 5.59a6.5 6.5 0 0 0 5.34-1.48l.27.28v.79l4.25 4.25c.41.41 1.08.41 1.49 0 .41-.41.41-1.08 0-1.49L15.5 14zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"></path>
            </svg>
          </div>
          <span class="navbar-text">地图操作</span>
        </button>

        <!-- 绘制工具 -->
        <button
          class="navbar-button"
          :class="{ active: drawToolsEnabled }"
          @click="toggleDrawTools"
        >
          <div class="navbar-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
              <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"></path>
            </svg>
          </div>
          <span class="navbar-text">绘制工具</span>
        </button>

        <!-- 测量工具 -->
        <button
          class="navbar-button"
          :class="{ active: measureToolsEnabled }"
          @click="toggleMeasureTools"
        >
          <div class="navbar-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
              <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm0 10H3V8h2v4h2V8h2v4h2V8h2v4h2V8h2v4h2V8h2v8z"></path>
            </svg>
          </div>
          <span class="navbar-text">测量工具</span>
        </button>
      </div>

      <!-- 右侧按钮 -->
      <div class="navbar-right">
        <!-- 定位按钮 -->
        <button
          class="navbar-button location-button"
          :class="{ active: positionEnabled }"
          @click="togglePosition"
        >
          <div class="navbar-icon">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="20" height="20" fill="currentColor">
              <path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm8.94 3A8.994 8.994 0 0 0 13 3.06V1h-2v2.06A8.994 8.994 0 0 0 3.06 11H1v2h2.06A8.994 8.994 0 0 0 11 20.94V23h2v-2.06A8.994 8.994 0 0 0 20.94 13H23v-2h-2.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"></path>
            </svg>
          </div>
          <span class="navbar-text">定位</span>
        </button>
      </div>
    </div>
  </div>
</template>

<style>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 64px;
  background: #fff;
  border-radius: 0 0 18px 18px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.08);
  z-index: 10000;
  display: flex;
  align-items: center;
  padding: 0 32px;
  transition: box-shadow 0.2s;
}

.navbar-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.navbar-logo {
  color: #1890ff;
  font-size: 22px;
  font-weight: 700;
  margin-right: 40px;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
}

.navbar-buttons {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.navbar-button {
  background: none;
  border: none;
  color: #333;
  font-size: 15px;
  padding: 0 28px;
  height: 48px;
  margin: 0 2px;
  cursor: pointer;
  border-radius: 12px;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  position: relative;
  display: flex;
  align-items: center;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.navbar-button .navbar-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  font-size: 18px;
}

.navbar-button:hover,
.navbar-button.active {
  background: #e6f7ff;
  color: #1890ff;
  box-shadow: 0 2px 8px rgba(24,144,255,0.10);
}

.navbar-text {
  font-size: 15px;
  font-weight: 500;
}

.navbar-right {
  display: flex;
  align-items: center;
  margin-left: 24px;
}

.location-button {
.navbar-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.08), transparent);
  transition: height 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.navbar-button:hover::before {
  height: 100%;
}

.navbar-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.navbar-text {
  font-weight: 500;
}

/* 各按钮激活状态 */
.navbar-button.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

/* 图层管理按钮激活状态 */
.navbar-button:nth-child(1).active {
  background-color: rgba(82, 196, 26, 0.15);
}

.navbar-button:nth-child(1).active::after {
  background-color: #52c41a;
  box-shadow: 0 0 10px rgba(82, 196, 26, 0.5);
}

/* 地图操作按钮激活状态 */
.navbar-button:nth-child(2).active {
  background-color: rgba(250, 140, 22, 0.15);
}

.navbar-button:nth-child(2).active::after {
  background-color: #fa8c16;
  box-shadow: 0 0 10px rgba(250, 140, 22, 0.5);
}

/* 绘制工具按钮激活状态 */
.navbar-button:nth-child(3).active {
  background-color: rgba(114, 46, 209, 0.15);
}

.navbar-button:nth-child(3).active::after {
  background-color: #722ed1;
  box-shadow: 0 0 10px rgba(114, 46, 209, 0.5);
}

/* 测量工具按钮激活状态 */
.navbar-button:nth-child(4).active {
  background-color: rgba(235, 47, 150, 0.15);
}

.navbar-button:nth-child(4).active::after {
  background-color: #eb2f96;
  box-shadow: 0 0 10px rgba(235, 47, 150, 0.5);
}

/* 定位按钮激活状态 */
.navbar-button.location-button.active {
  background-color: rgba(24, 144, 255, 0.15);
}

.navbar-button.location-button.active::after {
  background-color: #1890ff;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.5);
}

.navbar-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #1890ff;
  transform: scaleX(0.8);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.navbar-button.active:hover::after {
  transform: scaleX(1);
}

.navbar-right {
  display: flex;
  align-items: center;
}
}
</style>
