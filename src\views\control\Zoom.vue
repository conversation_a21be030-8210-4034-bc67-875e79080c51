<script setup>
import Zoom from 'ol/control/Zoom.js'
import ZoomSlider from 'ol/control/ZoomSlider.js'
import ZoomToExtent from 'ol/control/ZoomToExtent.js'
import { defineProps, watch, ref, onUnmounted } from 'vue'

const props = defineProps({
  map: Object
})

const zoomControl = ref(null)
const zoomSliderControl = ref(null)
const zoomToExtentControl = ref(null)

const createZoom = (map) => {
  if (!map) return

  removeZoomControls()

  const zoom = new Zoom()
  zoomControl.value = zoom

  const zoomSlider = new ZoomSlider()
  zoomSliderControl.value = zoomSlider

  const zoomToExtent = new ZoomToExtent({
    tipLabel: '缩放至全图范围' 
  })
  zoomToExtentControl.value = zoomToExtent

  // 缩放控件添加到地图
  map.addControl(zoom)
  map.addControl(zoomSlider)
  map.addControl(zoomToExtent)

  console.log('缩放控件已添加到地图')
}

// 移除缩放控件函数
const removeZoomControls = () => {
  if (props.map) {
    if (zoomControl.value) {
      props.map.removeControl(zoomControl.value)
      zoomControl.value = null
    }
    if (zoomSliderControl.value) {
      props.map.removeControl(zoomSliderControl.value)
      zoomSliderControl.value = null
    }
    if (zoomToExtentControl.value) {
      props.map.removeControl(zoomToExtentControl.value)
      zoomToExtentControl.value = null
    }
  }
}

// 监听地图变化
watch(() => props.map, (map) => {
  if (map) {
    createZoom(map)
  }
}, { immediate: true })

onUnmounted(() => {
  removeZoomControls()
})

defineExpose({
  createZoom,
  removeZoomControls
})
</script>

<template>
</template>

<style>
.ol-zoom {
  top: 32px;
  left: 32px;
  background: transparent;
  z-index: 1000;
}
.ol-zoom .ol-zoom-in,
.ol-zoom .ol-zoom-out {
  background: #fff;
  border: none;
  border-radius: 50%;
  margin-bottom: 8px;
  color: #1890ff;
  width: 28px;
  height: 28px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.18s, color 0.18s;
  box-shadow: none;
  font-weight: 600;
  padding: 0;
}
.ol-zoom .ol-zoom-in::after {
  content: '+';
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
}
.ol-zoom .ol-zoom-out::after {
  content: '-';
  font-size: 18px;
  font-weight: 600;
  line-height: 1;
}
.ol-zoom .ol-zoom-in span,
.ol-zoom .ol-zoom-out span {
  display: none;
}
.ol-zoom .ol-zoom-in:hover,
.ol-zoom .ol-zoom-out:hover {
  background: #e6f7ff;
  color: #096dd9;
}
.ol-zoomslider {
  top: 90px;
  left: 42px;
  height: 100px;
  width: 10px;
  background: ＃87CEFA;
  border-radius: 2px;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ol-zoomslider-thumb {
  background: #e6f7ff;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin: 0 -4px;
  box-shadow: none;
  border: 2px solid #fff;
  transition: background 0.18s;
}
.ol-zoomslider-thumb:hover {
  background: #096dd9;
}
.ol-zoom-extent {
  top: 200px;
  left: 32px;
}
.ol-zoom-extent button {
  background: #fff;
  border-radius: 50%;
  color: #1890ff;
  width: 28px;
  height: 28px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none;
  transition: background 0.18s, color 0.18s;
  padding: 0;
}
.ol-zoom-extent button::after {
  content: '\21BB';
  font-size: 16px;
}
.ol-zoom-extent button:hover {
  background: #e6f7ff;
  color: #096dd9;
}
</style>