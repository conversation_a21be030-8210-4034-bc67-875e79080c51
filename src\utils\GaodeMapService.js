
import axios from 'axios'

// Get Gaode Maps API key from global config
const getAmapKey = () => {
  if (window.gaodeConfig && window.gaodeConfig.apiKey) {
    return window.gaodeConfig.apiKey
  }
  console.warn('Gaode Maps API key not found in window.gaodeConfig, using fallback key')
  return 'your_amap_key_here' 
}

const AMAP_API_BASE = 'https://restapi.amap.com/v3'

/**
 * Geocode an address to get coordinates
 * @param {String} address - Address to geocode
 * @returns {Promise} Promise with geocoding result
 */
export async function geocodeAddress(address) {
  try {
    const response = await axios.get(`${AMAP_API_BASE}/geocode/geo`, {
      params: {
        key: getAmapKey(),
        address: address,
        output: 'JSON'
      }
    })
    return response.data
  } catch (error) {
    console.error('Geocoding error:', error)
    throw error
  }
}

/**
 * Reverse geocode coordinates to get address
 * @param {String} location - Coordinates in format "lng,lat"
 * @returns {Promise} Promise with reverse geocoding result
 */
export async function reverseGeocode(location) {
  try {
    const response = await axios.get(`${AMAP_API_BASE}/geocode/regeo`, {
      params: {
        key: getAmapKey(),
        location: location,
        output: 'JSON'
      }
    })
    return response.data
  } catch (error) {
    console.error('Reverse geocoding error:', error)
    throw error
  }
}

/**
 * Search for POIs by keyword
 * @param {String} keywords - Keywords to search for
 * @param {String} city - City to search in (optional)
 * @returns {Promise} Promise with POI search result
 */
export async function searchPOI(keywords, city = '') {
  try {
    const response = await axios.get(`${AMAP_API_BASE}/place/text`, {
      params: {
        key: getAmapKey(),
        keywords: keywords,
        city: city,
        output: 'JSON'
      }
    })
    return response.data
  } catch (error) {
    console.error('POI search error:', error)
    throw error
  }
}

/**
 * Get driving route between two points
 * @param {String} origin - Origin coordinates in format "lng,lat"
 * @param {String} destination - Destination coordinates in format "lng,lat"
 * @returns {Promise} Promise with route planning result
 */
export async function getDrivingRoute(origin, destination) {
  try {
    const response = await axios.get(`${AMAP_API_BASE}/direction/driving`, {
      params: {
        key: getAmapKey(),
        origin: origin,
        destination: destination,
        output: 'JSON',
        extensions: 'all'
      }
    })
    return response.data
  } catch (error) {
    console.error('Route planning error:', error)
    throw error
  }
}

/**
 * Get walking route between two points
 * @param {String} origin - Origin coordinates in format "lng,lat"
 * @param {String} destination - Destination coordinates in format "lng,lat"
 * @returns {Promise} Promise with route planning result
 */
export async function getWalkingRoute(origin, destination) {
  try {
    const response = await axios.get(`${AMAP_API_BASE}/direction/walking`, {
      params: {
        key: getAmapKey(),
        origin: origin,
        destination: destination,
        output: 'JSON'
      }
    })
    return response.data
  } catch (error) {
    console.error('Walking route error:', error)
    throw error
  }
}

/**
 * Get transit route between two points
 * @param {String} origin - Origin coordinates in format "lng,lat"
 * @param {String} destination - Destination coordinates in format "lng,lat"
 * @param {String} city - City for transit search
 * @returns {Promise} Promise with route planning result
 */
export async function getTransitRoute(origin, destination, city) {
  try {
    const response = await axios.get(`${AMAP_API_BASE}/direction/transit/integrated`, {
      params: {
        key: getAmapKey(),
        origin: origin,
        destination: destination,
        city: city,
        output: 'JSON'
      }
    })
    return response.data
  } catch (error) {
    console.error('Transit route error:', error)
    throw error
  }
}

/**
 * Convert coordinates from EPSG:3857 to Gaode Maps format (GCJ-02)
 * @param {Array} coordinates - Coordinates in EPSG:3857 format [x, y]
 * @returns {String} Coordinates in "lng,lat" format for Gaode Maps API
 */
export function convertToGaodeCoordinates(coordinates) {
  const x = coordinates[0]
  const y = coordinates[1]

  // Convert to radians
  const lon = (x / 20037508.34) * 180
  const lat = (Math.atan(Math.exp(y / 20037508.34 * Math.PI)) * 360) / Math.PI - 90

  // Return as string in "lng,lat" format
  return `${lon.toFixed(6)},${lat.toFixed(6)}`
}

/**
 * Parse Gaode route response and convert to GeoJSON for display on map
 * @param {Object} routeResponse - Response from Gaode route API
 * @returns {Object} GeoJSON object for display on map
 */
export function parseRouteToGeoJSON(routeResponse) {
  console.log('解析路线数据:', routeResponse);

  if (!routeResponse) {
    console.error('路线数据为空');
    return null;
  }

  // 检查是否有路线数据
  if (!routeResponse.route) {
    console.error('无路线数据 (route)');
    return null;
  }

  // 检查是否有路径数据
  if (!routeResponse.route.paths || routeResponse.route.paths.length === 0) {
    console.error('无路径数据 (paths)');
    return null;
  }

  try {
    // 获取第一条路径
    const path = routeResponse.route.paths[0];

    // 获取路径中的步骤
    const steps = path.steps || [];

    if (steps.length === 0) {
      console.error('路径中没有步骤数据');
      return null;
    }

    // 创建特征数组
    const features = [];

    // 处理每个步骤
    steps.forEach((step, index) => {
      // 获取折线数据
      const polyline = step.polyline;
      if (!polyline) {
        console.warn(`步骤 ${index} 没有折线数据`);
        return;
      }

      // 分割折线点
      const points = polyline.split(';').map(point => {
        const [lng, lat] = point.split(',').map(Number);
        if (isNaN(lng) || isNaN(lat)) {
          console.warn(`无效的坐标点: ${point}`);
          return null;
        }
        return [lng, lat];
      }).filter(point => point !== null);

      if (points.length < 2) {
        console.warn(`步骤 ${index} 的有效点数不足以形成线段`);
        return;
      }

      // 创建特征
      features.push({
        type: 'Feature',
        properties: {
          index: index,
          instruction: step.instruction || '',
          distance: step.distance || 0,
          duration: step.duration || 0
        },
        geometry: {
          type: 'LineString',
          coordinates: points
        }
      });
    });

    if (features.length === 0) {
      console.error('没有有效的路线特征');
      return null;
    }

    // 返回GeoJSON
    return {
      type: 'FeatureCollection',
      features: features
    };
  } catch (error) {
    console.error('解析路线数据时出错:', error);
    return null;
  }
}
