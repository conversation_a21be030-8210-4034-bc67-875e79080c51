<template>
  <div class="gaode-router-container">
  <button class="back-btn" @click="goBack" title="返回首页">← 返回</button>
    <div id="container" class="map-container"></div>
  
    <div id="panel" class="route-panel"></div>
  
    <!-- 搜索控制面板 -->
    <div class="search-controls search-controls-left">
      <div class="form-title">路线规划</div>
      <hr style="margin:0 0 10px 0;border:0;border-top:1px solid #eee;">
      <div class="search-type-selector">
        <el-radio-group v-model="searchType" @change="clearRouteDisplay">
          <el-radio-button label="coordinates">经纬度搜索</el-radio-button>
          <el-radio-button label="keywords">关键字搜索</el-radio-button>
        </el-radio-group>
      </div>
  
      <!-- 经纬度搜索 -->
      <div v-if="searchType === 'coordinates'" class="coordinates-search">
        <div class="input-group">
          <span class="input-label">起点:</span>
          <el-input v-model="startLng" placeholder="经度" size="small" style="width: 110px;"/>
          <el-input v-model="startLat" placeholder="纬度" size="small" style="width: 110px; margin-left: 5px;"/>
        </div>
        <div class="input-group">
          <span class="input-label">终点:</span>
          <el-input v-model="endLng" placeholder="经度" size="small" style="width: 110px;"/>
          <el-input v-model="endLat" placeholder="纬度" size="small" style="width: 110px; margin-left: 5px;"/>
        </div>
      </div>
  
      <!-- 关键词搜索 -->
      <div v-else class="keywords-search">
        <div class="input-group">
          <span class="input-label">起点:</span>
          <el-input v-model="startKeyword" placeholder="输入起点关键字" size="small" />
          <el-input v-model="startCity" placeholder="所在城市(可选)" size="small" style="width: 100px; margin-left: 5px;"/>
        </div>
        <div class="input-group">
          <span class="input-label">终点:</span>
          <el-input v-model="endKeyword" placeholder="输入终点关键字" size="small" />
          <el-input v-model="endCity" placeholder="所在城市(可选)" size="small" style="width: 100px; margin-left: 5px;"/>
        </div>
      </div>
  
      <div class="route-type-selector">
        <el-radio-group v-model="routeType" size="small">
          <el-radio-button label="driving">驾车</el-radio-button>
          <el-radio-button label="walking">步行</el-radio-button>
        </el-radio-group>
      </div>
  
      <div class="action-buttons">
        <el-button type="primary" @click="searchRoute" size="small">规划路线</el-button>
        <el-button @click="clearRouteDisplay" size="small">清除路线</el-button>
      </div>
    </div>
  </div>
  Use code with caution.
    </template>
    <script setup>
    import { ref, onMounted, onUnmounted, nextTick } from 'vue'
    import { ElMessage } from 'element-plus'
    import { useRouter } from 'vue-router'
    
    const FALLBACK_AMAP_KEY = '97ebc4c498d0dd277228f216e4ca90db';
    const FALLBACK_AMAP_SECURITY_JS_CODE = '968bc28bc90df063d4fe02b766191d3d'; 
    
    const getAmapKey = () => {
      if (window.gaodeConfig && window.gaodeConfig.apiKey) {
        return window.gaodeConfig.apiKey;
      }
      console.warn('Gaode Maps API key not found in window.gaodeConfig, using fallback key');
      return FALLBACK_AMAP_KEY;
    };
    
    const getAmapSecurityJsCode = () => {
      if (window.gaodeConfig && window.gaodeConfig.securityJsCode) {
        return window.gaodeConfig.securityJsCode;
      }
      console.warn('Gaode Maps securityJsCode not found in window.gaodeConfig, using fallback value');
      return FALLBACK_AMAP_SECURITY_JS_CODE;
    };
    
    const getAmapVersion = () => {
      if (window.gaodeConfig && window.gaodeConfig.version) {
        return window.gaodeConfig.version;
      }
      return '2.0'; 
    };
    
    const AMAP_PLUGINS = ['AMap.Driving', 'AMap.Walking', 'AMap.ToolBar', 'AMap.Scale', 'AMap.Geocoder'];
    
    const router = useRouter()
    const searchType = ref('coordinates')
    const routeType = ref('driving')
    
    const startLng = ref('116.379028')
    const startLat = ref('39.865042')
    const endLng = ref('116.427281')
    const endLat = ref('39.903719')
    
    const startKeyword = ref('北京西站')
    const startCity = ref('北京')
    const endKeyword = ref('北京大学')
    const endCity = ref('北京')
    
    let map = null
    let driving = null
    let walking = null
    let currentRouteInstance = null;
    
    let isApiLoaded = ref(false);
    let mapInitializationPromise = null;
  
    const checkAMapLoaded = () => typeof window.AMap !== 'undefined';
    
    const loadAMapScript = () => {
      if (document.getElementById('amap-api-script') || mapInitializationPromise) {
          return mapInitializationPromise || Promise.resolve(window.AMap); 
      }
    
      mapInitializationPromise = new Promise((resolve, reject) => {
        console.log('Setting up AMap security config and loading script...');
        const securityCode = getAmapSecurityJsCode();
        if (securityCode) {
          window._AMapSecurityConfig = {
            securityJsCode: securityCode,
          };
          console.log('AMap Security Config (securityJsCode) set with:', securityCode);
        } else {
          console.warn('AMap securityJsCode is not configured. This may cause issues if required by your API Key.');
        }
    
        const script = document.createElement('script');
        script.id = 'amap-api-script';
        script.type = 'text/javascript';
        script.async = true;
        script.src = `https://webapi.amap.com/maps?v=${getAmapVersion()}&key=${getAmapKey()}&plugin=${AMAP_PLUGINS.join(',')}`;
    
        script.onload = () => {
          console.log('AMap API script loaded successfully.');
          if (checkAMapLoaded()) {
            isApiLoaded.value = true;
            resolve(window.AMap);
          } else {
            const msg = 'AMap script loaded but AMap object is still undefined!';
            console.error(msg);
            ElMessage.error(msg);
            reject(new Error(msg));
          }
        };
        script.onerror = (err) => {
          console.error('Failed to load AMap API script:', err);
          ElMessage.error('Failed to load AMap API script. Check network or Key configuration.');
          reject(new Error('Failed to load AMap API script'));
          mapInitializationPromise = null;
        };
        document.head.appendChild(script);
      });
      return mapInitializationPromise;
    };
    
    const initMapAndPlugins = async () => {
      if (map) {
        console.log('Map already initialized.');
        return true;
      }
    
      if (!checkAMapLoaded()) {
        const msg = "Map API not loaded, cannot initialize map";
        console.error(msg);
        ElMessage.error(msg);
        return false;
      }
    
      console.log('Initializing map and plugins...');
    
      try {
        await nextTick();
        const container = document.getElementById('container');
        if (!container) {
          console.error('Map container #container not found!');
          ElMessage.error('Map container not found!');
          return false;
        }
    
        map = new AMap.Map('container', {
          resizeEnable: true,
          center: [116.397428, 39.90923],
          zoom: 13,
          viewMode: '2D',
        });
        console.log('Map instance created successfully');
    
        if (AMap.ToolBar) map.addControl(new AMap.ToolBar({ position: 'RB' }));
        if (AMap.Scale) map.addControl(new AMap.Scale());
        console.log('Map controls added (if available).');
    
        await initRoutePlugins();
    
        ElMessage.success('Map initialized successfully');
        return true;
    
      } catch (error) {
        console.error('Error during map initialization:', error);
        ElMessage.error(`Map initialization failed: ${error.message || error}`);
        cleanupMapResources();
        return false;
      }
    }
    
    const initRoutePlugins = async () => {
      return new Promise((resolve, reject) => {
          if (!window.AMap) {
              console.error('AMap object not found when initializing plugins.');
              return reject(new Error('AMap object not found'));
          }
    
          AMap.plugin(['AMap.Driving', 'AMap.Walking'], () => {
              try {
                  if (AMap.Driving) {
                      driving = new AMap.Driving({
                          map: map,
                          panel: 'panel',
                          policy: AMap.DrivingPolicy ? AMap.DrivingPolicy.LEAST_TIME : undefined, // 兼容老版本
                          autoFitView: true
                      });
                      console.log('Driving route instance created.');
                  } else {
                      console.error('AMap.Driving plugin not available after AMap.plugin call.');
                      ElMessage.warning('Driving route functionality may not be available.');
                  }
    
                  if (AMap.Walking) {
                      walking = new AMap.Walking({
                          map: map,
                          panel: 'panel',
                          autoFitView: true
                      });
                      console.log('Walking route instance created.');
                  } else {
                      console.error('AMap.Walking plugin not available after AMap.plugin call.');
                      ElMessage.warning('Walking route functionality may not be available.');
                  }
                  resolve();
              } catch (pluginError) {
                  console.error('Error creating route plugins:', pluginError);
                  ElMessage.error(`Error initializing route plugins: ${pluginError.message}`);
                  reject(pluginError);
              }
          });
      });
  }
    
    const cleanupMapResources = () => {
      if (driving) driving = null;
      if (walking) walking = null;
      currentRouteInstance = null;
      if (map) {
        try { map.destroy(); } catch (e) { console.error('Error destroying map:', e); }
        map = null;
      }
    }
    
    const searchRoute = async () => {
      if (!map) {
        ElMessage.warning('Map not initialized yet, please wait...');
        return;
      }
      clearRouteDisplay();
      if (routeType.value === 'driving') currentRouteInstance = driving;
      else if (routeType.value === 'walking') currentRouteInstance = walking;
      else { ElMessage.warning('This route type is not supported yet.'); return; }
    
      if (!currentRouteInstance) {
        ElMessage.error(`${routeType.value.charAt(0).toUpperCase() + routeType.value.slice(1)} route functionality is not available.`);
        return;
      }
      try {
        ElMessage.info(`Planning ${routeType.value} route...`);
        if (searchType.value === 'coordinates') await searchByCoordinates();
        else await searchByKeywords();
      } catch (error) {
        ElMessage.error(`Route search failed: ${error.message || error}`);
      }
    }
    
    const searchByCoordinates = async () => {
      if (!startLng.value || !startLat.value || !endLng.value || !endLat.value) {
        ElMessage.warning('Please enter complete start and end coordinates.'); return;
      }
      const startPoint = new AMap.LngLat(parseFloat(startLng.value), parseFloat(startLat.value));
      const endPoint = new AMap.LngLat(parseFloat(endLng.value), parseFloat(endLat.value));
      if (isNaN(startPoint.getLng()) || isNaN(startPoint.getLat()) || isNaN(endPoint.getLng()) || isNaN(endPoint.getLat())) {
          ElMessage.warning('Invalid coordinates. Please enter valid numbers.'); return;
      }
      return new Promise((resolve, reject) => {
        currentRouteInstance.search(startPoint, endPoint, (status, result) => {
          handleRouteResult(status, result, routeType.value);
          if (status === 'complete' || status === 'no_data') resolve(); else reject(result);
        });
      });
    }
    
    const searchByKeywords = async () => {
      if (!startKeyword.value || !endKeyword.value) {
        ElMessage.warning('Please enter start and end keywords.'); return;
      }
      const searchPointsArray = [
        { keyword: startKeyword.value, city: startCity.value || undefined },
        { keyword: endKeyword.value, city: endCity.value || undefined }
      ];
      return new Promise((resolve, reject) => {
        currentRouteInstance.search(searchPointsArray, (status, result) => {
          handleRouteResult(status, result, routeType.value);
          if (status === 'complete' || status === 'no_data') resolve(); else reject(result);
        });
      });
    }
    
    const handleRouteResult = (status, result, type) => {
      if (status === 'complete') {
        if (result && result.routes && result.routes.length > 0) {
          ElMessage.success(`${type} route planned. Dist: ${(result.routes[0].distance / 1000).toFixed(2)} km`);
          if (currentRouteInstance && typeof currentRouteInstance.setFitView === 'function') {
            try { currentRouteInstance.setFitView(); } catch (e) { console.warn('Could not setFitView:', e); }
          }
        } else {
          ElMessage.warning(`${type} route planning complete, but no valid path found.`);
        }
      } else if (status === 'error') {
        const errorMsg = result && (result.info || result.message) ? (result.info || result.message) : 'Unknown error';
        ElMessage.error(`${type} route planning failed: ${errorMsg}`);
      } else if (status === 'no_data') {
        ElMessage.info(`No relevant ${type} route data found.`);
      } else {
        ElMessage.error(`${type} route planning status: ${status}`);
      }
    }
    
    const clearRouteDisplay = () => {
      if (driving) try { driving.clear(); } catch (e) { console.error('Error clearing driving:', e); }
      if (walking) try { walking.clear(); } catch (e) { console.error('Error clearing walking:', e); }
      const panel = document.getElementById('panel');
      if (panel) panel.innerHTML = '';
    }
    
    const goBack = () => { router.push('/'); }
    
    onMounted(async () => {
      console.log('gaodeDriverrouter.vue Component Mounted');
      try {
        if (window.AMap) {
          isApiLoaded.value = true;
          await initMapAndPlugins();
        } else {
          await loadAMapScript();
          await mapInitializationPromise;
          await initMapAndPlugins();
        }
      } catch (error) {
        console.error("Component mount initialization failed:", error);
        ElMessage.error(`Initialization failed: ${error.message || error}`);
      }
    });
    
    onUnmounted(() => {
      console.log('gaodeDriverrouter.vue Component Unmounted');
      clearRouteDisplay();
      cleanupMapResources();
      mapInitializationPromise = null; 
      isApiLoaded.value = false;
      console.log('Resources cleaned up.');
    });
    
    </script>
    <style>
    .gaode-router-container {
      width: 100%;
      height: 100%;
      position: relative; 
      background-color: #f5f7fa;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .back-btn {
      position: absolute;
      top: 18px;
      left: 18px;
      z-index: 2000;
      background: #fff;
      color: #333;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      padding: 6px 18px 6px 12px;
      font-size: 15px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      transition: background 0.2s, color 0.2s;
    }
    .back-btn:hover {
      background: #f5f7fa;
      color: #409EFF;
      border-color: #b3d8fd;
    }
    
    .map-container {
      flex-grow: 1;
      width: 100%;
      position: relative;
      z-index: 1;    
    }
    
    .route-panel {
      position: absolute;
      background-color: rgba(255, 255, 255, 0.95);
      max-height: 60%;
      overflow-y: auto;
      top: 60px; 
      right: 10px;
      width: 300px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.15);
      border-radius: 4px;
      z-index: 1000; 
      transition: all 0.3s ease;
    }
    
    .route-panel::-webkit-scrollbar {
        width: 6px;
    }
    .route-panel::-webkit-scrollbar-thumb {
        background: #ccc;
        border-radius: 3px;
    }
    
    .route-panel .amap-call {
      background-color: #409EFF;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      color: white;
      padding: 8px 12px;
    }
    .route-panel .amap-lib-driving,
    .route-panel .amap-lib-walking {
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      overflow: hidden;
      padding: 10px;
      font-size: 13px;
    }
    
    .search-controls {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background: rgba(255,255,255,0.98);
      box-shadow: 0 -2px 12px rgba(0,0,0,0.06);
      padding: 18px 24px 12px 24px;
      z-index: 1200;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    .search-controls-left {
      position: absolute;
      top: 25vh; 
      left: 36px;
      right: auto;
      bottom: auto;
      width: 290px;
      min-height: 220px;
      max-height: 70vh;
      background: rgba(255,255,255,0.97);
      border-radius: 18px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.14);
      padding: 18px 18px 14px 18px;
      display: flex;
      flex-direction: column;
      gap: 14px;
      z-index: 1200;
      border: 1px solid #f0f0f0;
      transition: box-shadow 0.2s, background 0.2s;
      font-family: 'Segoe UI', 'PingFang SC', 'Hiragino Sans', 'Arial', sans-serif;
    }
    .search-controls-left:hover {
      box-shadow: 0 14px 40px rgba(0,0,0,0.18);
      background: #fff;
    }
    .search-controls-left .input-group {
      gap: 6px;
      margin-bottom: 6px;
    }
    .search-controls-left .input-label {
      width: 36px;
      color: #666;
      font-size: 14px;
      margin-right: 2px;
      text-align: right;
    }
    .search-controls-left .el-input {
      flex: 1;
      min-width: 0;
      font-size: 13px;
    }
    .search-controls-left .route-type-selector {
      margin: 8px 0 0 0;
    }
    .search-controls-left .action-buttons {
      gap: 8px;
      margin-top: 8px;
    }
    .search-controls-left .el-button {
      border-radius: 8px;
      font-size: 13px;
      padding: 4px 14px;
    }
    .search-controls-left .search-type-selector {
      margin-bottom: 6px;
    }
    .search-controls-left .el-radio-button__inner {
      border-radius: 8px !important;
      font-size: 13px;
      padding: 2px 10px;
    }
    .search-controls-left .el-radio-group {
      gap: 4px;
    }
    .search-controls-left hr {
      margin: 0 0 8px 0;
      border: 0;
      border-top: 1px solid #f2f2f2;
    }
    .search-controls-left .form-title {
      font-weight: 600;
      font-size: 16px;
      margin-bottom: 6px;
      text-align: center;
      color: #409EFF;
      letter-spacing: 1px;
    }
    .search-type-selector {
      display: flex;
      justify-content: center;
      margin-bottom: 10px;
    }
    .input-group {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 10px;
    }
    .input-label {
      width: 40px;
      color: #888;
      font-size: 15px;
      margin-right: 4px;
      text-align: right;
    }
    .el-input {
      flex: 1;
      min-width: 0;
    }
    .route-type-selector {
      display: flex;
      justify-content: center;
      margin: 12px 0 0 0;
    }
    .action-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 12px;
    }
    
    @media (max-width: 900px) {
      .search-controls-left {
        width: 98vw;
        left: 1vw;
        right: 1vw;
        min-width: 0;
        padding: 10px 4px 8px 4px;
        top: 10px;
      }
    }
    </style>