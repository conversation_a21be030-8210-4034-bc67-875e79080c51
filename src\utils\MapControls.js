
/**
 * Zoom in the map view by one level
 * @param {Object} map - OpenLayers Map instance
 */
export function zoomIn(map) {
  if (!map) return
  const view = map.getView()
  const curZoom = view.getZoom()
  view.setZoom(curZoom + 1)
}

/**
 * Zoom out the map view by one level
 * @param {Object} map - OpenLayers Map instance
 */
export function zoomOut(map) {
  if (!map) return
  const view = map.getView()
  const curZoom = view.getZoom()
  view.setZoom(curZoom - 1)
}

/**
 * Move the map view to Wuhan
 * @param {Object} map - OpenLayers Map instance
 */
export function moveToWuhan(map) {
  if (!map) return
  const view = map.getView()
  view.setCenter([12727039.383734727, 3579066.6894065146])
  view.setZoom(12)
}

/**
 * Reset the map view to its initial state
 * @param {Object} map - OpenLayers Map instance
 * @param {Number} initialZoom - Initial zoom level
 * @param {Array} initialCenter - Initial center coordinates
 * @param {Number} initialRotation - Initial rotation
 */
export function resetMap(map, initialZoom, initialCenter, initialRotation) {
  if (!map || initialZoom === null) return
  const view = map.getView()
  view.setZoom(initialZoom)
  view.setCenter(initialCenter)
  view.setRotation(initialRotation)
}

/**
 * Update the map size when window is resized
 * @param {Object} map - OpenLayers Map instance
 */
export function updateMapSize(map) {
  if (map) {
    map.updateSize()
  }
}
