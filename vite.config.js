import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  // 添加构建选项，优化缓存和错误处理
  build: {
    // 清除构建缓存
    emptyOutDir: true,
    // 改进错误处理
    rollupOptions: {
      onwarn(warning, warn) {
        // 忽略特定的警告
        if (warning.code === 'MODULE_NOT_FOUND' &&
            warning.message &&
            warning.message.includes('CitySearch.vue')) {
          return;
        }
        warn(warning);
      }
    }
  }
})
